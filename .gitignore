# Django + Vue 混合项目 .gitignore

# ==================== 环境配置和敏感文件 ====================
# 环境配置文件（包含敏感信息）
.env
.env.local
.env.*.local
.env.backup
dev_config.py

# 开发工具文件（不提交到Git）
final_review_gate.py
.env.example

# ==================== 开发工具和IDE配置 ====================
# Kiro IDE 配置文件夹（不提交到Git）
.kiro/

# 文档文件夹（不提交到Git）
docs/
API_请求汇总文档.md
API_请求详细文档.md
css_js_integration_task.md
test*

# IDE 配置文件
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.settings/
*.sublime-project
*.sublime-workspace
.augment/

# ==================== Python 相关 ====================
# Python 字节码文件和缓存
**/__pycache__/
__pycache__/
*.py[cod]
*$py.class
*.pyc
*.pyo
*.pyd
*.cpython-*.pyc
*.cpython-*.pyo
.github/

# Python 分发和打包
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Python 虚拟环境
.venv/
venv/
ENV/
env/
.env/
.virtualenv/

# Python 测试和覆盖率
.coverage
.pytest_cache/
.tox/
.nox/
htmlcov/
.coverage.*
coverage.xml
*.cover
.hypothesis/

# ==================== Django 相关 ====================
# Django 日志和数据库
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
*.db

# Django 数据库迁移文件（自动生成的迁移文件不应提交到版本控制）
*/migrations/[0-9]*.py
*/migrations/__pycache__/
# 保留迁移目录的__init__.py文件（Python包必需文件）
!*/migrations/__init__.py

# Django 静态文件收集目录
staticfiles/
static/admin/
static/rest_framework/

# Django 会话和缓存
django_cache/
.django_cache/

# ==================== 前端开发相关 ====================
# Node.js 依赖
frontend/node_modules/
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# npm 和 yarn 缓存
.npm/
.yarn/
.pnp.*
.yarn-integrity

# 前端构建输出
frontend/dist/
frontend/build/
static/dist/
static/build/

# Vue 构建输出（服务器端构建策略）
templates/vue/
templates/vue/assets/
# 保留Vue模板目录结构
!templates/vue/
!templates/vue/pages/
!templates/vue/pages/.gitkeep

static/vue/

# ==================== Vue 和 Vite 相关 ====================
# Vite 缓存和临时文件
.vite/
frontend/.vite/
frontend/node_modules/.vite/

# Vue 编译缓存
cache/vue/
temp/vue/
.vue-cache/

# 前端工具缓存
.eslintcache
.stylelintcache
.postcssrc.js.cache
.parcel-cache/

# ==================== 媒体文件和上传内容 ====================
# 媒体文件（用户上传的内容，通常不应提交）
media/
static/images/
uploads/

# 备份文件夹
backup/

# ==================== 日志和临时文件 ====================
# 各种日志文件
*.log
logs/
*.log.*

# 临时文件
*.tmp
*.temp
*.swp
*.swo
*~
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 备份文件
*.bak
*.backup
*.orig

# ==================== 操作系统相关 ====================
# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ==================== 其他工具和缓存 ====================
# Webpack
webpack-bundle-report.html
webpack-stats.json

# 测试覆盖率
coverage/
.nyc_output/

# 性能分析
.prof

# 编辑器临时文件
.vscode/settings.json
.vscode/launch.json
.vscode/extensions.json

# 特定文件（根据项目需要调整）
remove_console_logs.py
README_VUE_HYBRID.md

README_VUE_HYBRID.md
backup_before_refactor/

templates\vue\
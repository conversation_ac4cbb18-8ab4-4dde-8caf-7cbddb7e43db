var __defProp=Object.defineProperty,__defProps=Object.defineProperties,__getOwnPropDescs=Object.getOwnPropertyDescriptors,__getOwnPropSymbols=Object.getOwnPropertySymbols,__hasOwnProp=Object.prototype.hasOwnProperty,__propIsEnum=Object.prototype.propertyIsEnumerable,__defNormalProp=(e,t,n)=>t in e?__defProp(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,__spreadValues=(e,t)=>{for(var n in t||(t={}))__hasOwnProp.call(t,n)&&__defNormalProp(e,n,t[n]);if(__getOwnPropSymbols)for(var n of __getOwnPropSymbols(t))__propIsEnum.call(t,n)&&__defNormalProp(e,n,t[n]);return e},__spreadProps=(e,t)=>__defProps(e,__getOwnPropDescs(t)),__async=(e,t,n)=>new Promise((s,r)=>{var o=e=>{try{a(n.next(e))}catch(t){r(t)}},i=e=>{try{a(n.throw(e))}catch(t){r(t)}},a=e=>e.done?s(e.value):Promise.resolve(e.value).then(o,i);a((n=n.apply(e,t)).next())});
/**
* @vue/shared v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/
/*! #__NO_SIDE_EFFECTS__ */
function makeMap(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return e=>e in t}const EMPTY_OBJ={},EMPTY_ARR=[],NOOP=()=>{},NO=()=>!1,isOn=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),isModelListener=e=>e.startsWith("onUpdate:"),extend=Object.assign,remove=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},hasOwnProperty$1=Object.prototype.hasOwnProperty,hasOwn=(e,t)=>hasOwnProperty$1.call(e,t),isArray=Array.isArray,isMap=e=>"[object Map]"===toTypeString(e),isSet=e=>"[object Set]"===toTypeString(e),isDate=e=>"[object Date]"===toTypeString(e),isRegExp=e=>"[object RegExp]"===toTypeString(e),isFunction=e=>"function"==typeof e,isString=e=>"string"==typeof e,isSymbol=e=>"symbol"==typeof e,isObject=e=>null!==e&&"object"==typeof e,isPromise=e=>(isObject(e)||isFunction(e))&&isFunction(e.then)&&isFunction(e.catch),objectToString=Object.prototype.toString,toTypeString=e=>objectToString.call(e),toRawType=e=>toTypeString(e).slice(8,-1),isPlainObject=e=>"[object Object]"===toTypeString(e),isIntegerKey=e=>isString(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,isReservedProp=makeMap(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),isBuiltInDirective=makeMap("bind,cloak,else-if,else,for,html,if,model,on,once,pre,show,slot,text,memo"),cacheStringFunction=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},camelizeRE=/-(\w)/g,camelize=cacheStringFunction(e=>e.replace(camelizeRE,(e,t)=>t?t.toUpperCase():"")),hyphenateRE=/\B([A-Z])/g,hyphenate=cacheStringFunction(e=>e.replace(hyphenateRE,"-$1").toLowerCase()),capitalize=cacheStringFunction(e=>e.charAt(0).toUpperCase()+e.slice(1)),toHandlerKey=cacheStringFunction(e=>e?`on${capitalize(e)}`:""),hasChanged=(e,t)=>!Object.is(e,t),invokeArrayFns=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},def=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},looseToNumber=e=>{const t=parseFloat(e);return isNaN(t)?e:t},toNumber=e=>{const t=isString(e)?Number(e):NaN;return isNaN(t)?e:t};let _globalThis;const getGlobalThis=()=>_globalThis||(_globalThis="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{});function genCacheKey(e,t){return e+JSON.stringify(t,(e,t)=>"function"==typeof t?t.toString():t)}const GLOBALS_ALLOWED="Infinity,undefined,NaN,isFinite,isNaN,parseFloat,parseInt,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,Math,Number,Date,Array,Object,Boolean,String,RegExp,Map,Set,JSON,Intl,BigInt,console,Error,Symbol",isGloballyAllowed=makeMap(GLOBALS_ALLOWED);function normalizeStyle(e){if(isArray(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=isString(s)?parseStringStyle(s):normalizeStyle(s);if(r)for(const e in r)t[e]=r[e]}return t}if(isString(e)||isObject(e))return e}const listDelimiterRE=/;(?![^(]*\))/g,propertyDelimiterRE=/:([^]+)/,styleCommentRE=/\/\*[^]*?\*\//g;function parseStringStyle(e){const t={};return e.replace(styleCommentRE,"").split(listDelimiterRE).forEach(e=>{if(e){const n=e.split(propertyDelimiterRE);n.length>1&&(t[n[0].trim()]=n[1].trim())}}),t}function normalizeClass(e){let t="";if(isString(e))t=e;else if(isArray(e))for(let n=0;n<e.length;n++){const s=normalizeClass(e[n]);s&&(t+=s+" ")}else if(isObject(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}function normalizeProps(e){if(!e)return null;let{class:t,style:n}=e;return t&&!isString(t)&&(e.class=normalizeClass(t)),n&&(e.style=normalizeStyle(n)),e}const HTML_TAGS="html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot",SVG_TAGS="svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistantLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view",MATH_TAGS="annotation,annotation-xml,maction,maligngroup,malignmark,math,menclose,merror,mfenced,mfrac,mfraction,mglyph,mi,mlabeledtr,mlongdiv,mmultiscripts,mn,mo,mover,mpadded,mphantom,mprescripts,mroot,mrow,ms,mscarries,mscarry,msgroup,msline,mspace,msqrt,msrow,mstack,mstyle,msub,msubsup,msup,mtable,mtd,mtext,mtr,munder,munderover,none,semantics",VOID_TAGS="area,base,br,col,embed,hr,img,input,link,meta,param,source,track,wbr",isHTMLTag=makeMap(HTML_TAGS),isSVGTag=makeMap(SVG_TAGS),isMathMLTag=makeMap(MATH_TAGS),isVoidTag=makeMap(VOID_TAGS),specialBooleanAttrs="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",isSpecialBooleanAttr=makeMap(specialBooleanAttrs);function includeBooleanAttr(e){return!!e||""===e}function looseCompareArrays(e,t){if(e.length!==t.length)return!1;let n=!0;for(let s=0;n&&s<e.length;s++)n=looseEqual(e[s],t[s]);return n}function looseEqual(e,t){if(e===t)return!0;let n=isDate(e),s=isDate(t);if(n||s)return!(!n||!s)&&e.getTime()===t.getTime();if(n=isSymbol(e),s=isSymbol(t),n||s)return e===t;if(n=isArray(e),s=isArray(t),n||s)return!(!n||!s)&&looseCompareArrays(e,t);if(n=isObject(e),s=isObject(t),n||s){if(!n||!s)return!1;if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e){const s=e.hasOwnProperty(n),r=t.hasOwnProperty(n);if(s&&!r||!s&&r||!looseEqual(e[n],t[n]))return!1}}return String(e)===String(t)}function looseIndexOf(e,t){return e.findIndex(e=>looseEqual(e,t))}const isRef$1=e=>!(!e||!0!==e.__v_isRef),toDisplayString=e=>isString(e)?e:null==e?"":isArray(e)||isObject(e)&&(e.toString===objectToString||!isFunction(e.toString))?isRef$1(e)?toDisplayString(e.value):JSON.stringify(e,replacer,2):String(e),replacer=(e,t)=>isRef$1(t)?replacer(e,t.value):isMap(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((e,[t,n],s)=>(e[stringifySymbol(t,s)+" =>"]=n,e),{})}:isSet(t)?{[`Set(${t.size})`]:[...t.values()].map(e=>stringifySymbol(e))}:isSymbol(t)?stringifySymbol(t):!isObject(t)||isArray(t)||isPlainObject(t)?t:String(t),stringifySymbol=(e,t="")=>{var n;return isSymbol(e)?`Symbol(${null!=(n=e.description)?n:t})`:e};function normalizeCssVarValue(e){return null==e?"initial":"string"==typeof e?""===e?" ":e:String(e)}
/**
* @vue/reactivity v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let activeEffectScope,activeSub;class EffectScope{constructor(e=!1){this.detached=e,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=activeEffectScope,!e&&activeEffectScope&&(this.index=(activeEffectScope.scopes||(activeEffectScope.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){let e,t;if(this._isPaused=!0,this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].pause();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].pause()}}resume(){if(this._active&&this._isPaused){let e,t;if(this._isPaused=!1,this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].resume();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].resume()}}run(e){if(this._active){const t=activeEffectScope;try{return activeEffectScope=this,e()}finally{activeEffectScope=t}}}on(){1===++this._on&&(this.prevScope=activeEffectScope,activeEffectScope=this)}off(){this._on>0&&0===--this._on&&(activeEffectScope=this.prevScope,this.prevScope=void 0)}stop(e){if(this._active){let t,n;for(this._active=!1,t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(this.effects.length=0,t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.cleanups.length=0,this.scopes){for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0}}}function effectScope(e){return new EffectScope(e)}function getCurrentScope(){return activeEffectScope}function onScopeDispose(e,t=!1){activeEffectScope&&activeEffectScope.cleanups.push(e)}const pausedQueueEffects=new WeakSet;class ReactiveEffect{constructor(e){this.fn=e,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,activeEffectScope&&activeEffectScope.active&&activeEffectScope.effects.push(this)}pause(){this.flags|=64}resume(){64&this.flags&&(this.flags&=-65,pausedQueueEffects.has(this)&&(pausedQueueEffects.delete(this),this.trigger()))}notify(){2&this.flags&&!(32&this.flags)||8&this.flags||batch(this)}run(){if(!(1&this.flags))return this.fn();this.flags|=2,cleanupEffect(this),prepareDeps(this);const e=activeSub,t=shouldTrack;activeSub=this,shouldTrack=!0;try{return this.fn()}finally{cleanupDeps(this),activeSub=e,shouldTrack=t,this.flags&=-3}}stop(){if(1&this.flags){for(let e=this.deps;e;e=e.nextDep)removeSub(e);this.deps=this.depsTail=void 0,cleanupEffect(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){64&this.flags?pausedQueueEffects.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){isDirty(this)&&this.run()}get dirty(){return isDirty(this)}}let batchDepth=0,batchedSub,batchedComputed;function batch(e,t=!1){if(e.flags|=8,t)return e.next=batchedComputed,void(batchedComputed=e);e.next=batchedSub,batchedSub=e}function startBatch(){batchDepth++}function endBatch(){if(--batchDepth>0)return;if(batchedComputed){let e=batchedComputed;for(batchedComputed=void 0;e;){const t=e.next;e.next=void 0,e.flags&=-9,e=t}}let e;for(;batchedSub;){let n=batchedSub;for(batchedSub=void 0;n;){const s=n.next;if(n.next=void 0,n.flags&=-9,1&n.flags)try{n.trigger()}catch(t){e||(e=t)}n=s}}if(e)throw e}function prepareDeps(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function cleanupDeps(e){let t,n=e.depsTail,s=n;for(;s;){const e=s.prevDep;-1===s.version?(s===n&&(n=e),removeSub(s),removeDep(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=e}e.deps=t,e.depsTail=n}function isDirty(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(refreshComputed(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function refreshComputed(e){if(4&e.flags&&!(16&e.flags))return;if(e.flags&=-17,e.globalVersion===globalVersion)return;if(e.globalVersion=globalVersion,!e.isSSR&&128&e.flags&&(!e.deps&&!e._dirty||!isDirty(e)))return;e.flags|=2;const t=e.dep,n=activeSub,s=shouldTrack;activeSub=e,shouldTrack=!0;try{prepareDeps(e);const n=e.fn(e._value);(0===t.version||hasChanged(n,e._value))&&(e.flags|=128,e._value=n,t.version++)}catch(r){throw t.version++,r}finally{activeSub=n,shouldTrack=s,cleanupDeps(e),e.flags&=-3}}function removeSub(e,t=!1){const{dep:n,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let e=n.computed.deps;e;e=e.nextDep)removeSub(e,!0)}t||--n.sc||!n.map||n.map.delete(n.key)}function removeDep(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}function effect(e,t){e.effect instanceof ReactiveEffect&&(e=e.effect.fn);const n=new ReactiveEffect(e);t&&extend(n,t);try{n.run()}catch(r){throw n.stop(),r}const s=n.run.bind(n);return s.effect=n,s}function stop(e){e.effect.stop()}let shouldTrack=!0;const trackStack=[];function pauseTracking(){trackStack.push(shouldTrack),shouldTrack=!1}function resetTracking(){const e=trackStack.pop();shouldTrack=void 0===e||e}function cleanupEffect(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const e=activeSub;activeSub=void 0;try{t()}finally{activeSub=e}}}let globalVersion=0;class Link{constructor(e,t){this.sub=e,this.dep=t,this.version=t.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Dep{constructor(e){this.computed=e,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(e){if(!activeSub||!shouldTrack||activeSub===this.computed)return;let t=this.activeLink;if(void 0===t||t.sub!==activeSub)t=this.activeLink=new Link(activeSub,this),activeSub.deps?(t.prevDep=activeSub.depsTail,activeSub.depsTail.nextDep=t,activeSub.depsTail=t):activeSub.deps=activeSub.depsTail=t,addSub(t);else if(-1===t.version&&(t.version=this.version,t.nextDep)){const e=t.nextDep;e.prevDep=t.prevDep,t.prevDep&&(t.prevDep.nextDep=e),t.prevDep=activeSub.depsTail,t.nextDep=void 0,activeSub.depsTail.nextDep=t,activeSub.depsTail=t,activeSub.deps===t&&(activeSub.deps=e)}return t}trigger(e){this.version++,globalVersion++,this.notify(e)}notify(e){startBatch();try{0;for(let e=this.subs;e;e=e.prevSub)e.sub.notify()&&e.sub.dep.notify()}finally{endBatch()}}}function addSub(e){if(e.dep.sc++,4&e.sub.flags){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let e=t.deps;e;e=e.nextDep)addSub(e)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const targetMap=new WeakMap,ITERATE_KEY=Symbol(""),MAP_KEY_ITERATE_KEY=Symbol(""),ARRAY_ITERATE_KEY=Symbol("");function track(e,t,n){if(shouldTrack&&activeSub){let t=targetMap.get(e);t||targetMap.set(e,t=new Map);let s=t.get(n);s||(t.set(n,s=new Dep),s.map=t,s.key=n),s.track()}}function trigger(e,t,n,s,r,o){const i=targetMap.get(e);if(!i)return void globalVersion++;const a=e=>{e&&e.trigger()};if(startBatch(),"clear"===t)i.forEach(a);else{const r=isArray(e),o=r&&isIntegerKey(n);if(r&&"length"===n){const e=Number(s);i.forEach((t,n)=>{("length"===n||n===ARRAY_ITERATE_KEY||!isSymbol(n)&&n>=e)&&a(t)})}else switch((void 0!==n||i.has(void 0))&&a(i.get(n)),o&&a(i.get(ARRAY_ITERATE_KEY)),t){case"add":r?o&&a(i.get("length")):(a(i.get(ITERATE_KEY)),isMap(e)&&a(i.get(MAP_KEY_ITERATE_KEY)));break;case"delete":r||(a(i.get(ITERATE_KEY)),isMap(e)&&a(i.get(MAP_KEY_ITERATE_KEY)));break;case"set":isMap(e)&&a(i.get(ITERATE_KEY))}}endBatch()}function getDepFromReactive(e,t){const n=targetMap.get(e);return n&&n.get(t)}function reactiveReadArray(e){const t=toRaw(e);return t===e?t:(track(t,"iterate",ARRAY_ITERATE_KEY),isShallow(e)?t:t.map(toReactive))}function shallowReadArray(e){return track(e=toRaw(e),"iterate",ARRAY_ITERATE_KEY),e}const arrayInstrumentations={__proto__:null,[Symbol.iterator](){return iterator(this,Symbol.iterator,toReactive)},concat(...e){return reactiveReadArray(this).concat(...e.map(e=>isArray(e)?reactiveReadArray(e):e))},entries(){return iterator(this,"entries",e=>(e[1]=toReactive(e[1]),e))},every(e,t){return apply(this,"every",e,t,void 0,arguments)},filter(e,t){return apply(this,"filter",e,t,e=>e.map(toReactive),arguments)},find(e,t){return apply(this,"find",e,t,toReactive,arguments)},findIndex(e,t){return apply(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return apply(this,"findLast",e,t,toReactive,arguments)},findLastIndex(e,t){return apply(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return apply(this,"forEach",e,t,void 0,arguments)},includes(...e){return searchProxy(this,"includes",e)},indexOf(...e){return searchProxy(this,"indexOf",e)},join(e){return reactiveReadArray(this).join(e)},lastIndexOf(...e){return searchProxy(this,"lastIndexOf",e)},map(e,t){return apply(this,"map",e,t,void 0,arguments)},pop(){return noTracking(this,"pop")},push(...e){return noTracking(this,"push",e)},reduce(e,...t){return reduce(this,"reduce",e,t)},reduceRight(e,...t){return reduce(this,"reduceRight",e,t)},shift(){return noTracking(this,"shift")},some(e,t){return apply(this,"some",e,t,void 0,arguments)},splice(...e){return noTracking(this,"splice",e)},toReversed(){return reactiveReadArray(this).toReversed()},toSorted(e){return reactiveReadArray(this).toSorted(e)},toSpliced(...e){return reactiveReadArray(this).toSpliced(...e)},unshift(...e){return noTracking(this,"unshift",e)},values(){return iterator(this,"values",toReactive)}};function iterator(e,t,n){const s=shallowReadArray(e),r=s[t]();return s===e||isShallow(e)||(r._next=r.next,r.next=()=>{const e=r._next();return e.value&&(e.value=n(e.value)),e}),r}const arrayProto=Array.prototype;function apply(e,t,n,s,r,o){const i=shallowReadArray(e),a=i!==e&&!isShallow(e),c=i[t];if(c!==arrayProto[t]){const t=c.apply(e,o);return a?toReactive(t):t}let l=n;i!==e&&(a?l=function(t,s){return n.call(this,toReactive(t),s,e)}:n.length>2&&(l=function(t,s){return n.call(this,t,s,e)}));const d=c.call(i,l,s);return a&&r?r(d):d}function reduce(e,t,n,s){const r=shallowReadArray(e);let o=n;return r!==e&&(isShallow(e)?n.length>3&&(o=function(t,s,r){return n.call(this,t,s,r,e)}):o=function(t,s,r){return n.call(this,t,toReactive(s),r,e)}),r[t](o,...s)}function searchProxy(e,t,n){const s=toRaw(e);track(s,"iterate",ARRAY_ITERATE_KEY);const r=s[t](...n);return-1!==r&&!1!==r||!isProxy(n[0])?r:(n[0]=toRaw(n[0]),s[t](...n))}function noTracking(e,t,n=[]){pauseTracking(),startBatch();const s=toRaw(e)[t].apply(e,n);return endBatch(),resetTracking(),s}const isNonTrackableKeys=makeMap("__proto__,__v_isRef,__isVue"),builtInSymbols=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>"arguments"!==e&&"caller"!==e).map(e=>Symbol[e]).filter(isSymbol));function hasOwnProperty(e){isSymbol(e)||(e=String(e));const t=toRaw(this);return track(t,"has",e),t.hasOwnProperty(e)}class BaseReactiveHandler{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,n){if("__v_skip"===t)return e.__v_skip;const s=this._isReadonly,r=this._isShallow;if("__v_isReactive"===t)return!s;if("__v_isReadonly"===t)return s;if("__v_isShallow"===t)return r;if("__v_raw"===t)return n===(s?r?shallowReadonlyMap:readonlyMap:r?shallowReactiveMap:reactiveMap).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;const o=isArray(e);if(!s){let e;if(o&&(e=arrayInstrumentations[t]))return e;if("hasOwnProperty"===t)return hasOwnProperty}const i=Reflect.get(e,t,isRef(e)?e:n);return(isSymbol(t)?builtInSymbols.has(t):isNonTrackableKeys(t))?i:(s||track(e,"get",t),r?i:isRef(i)?o&&isIntegerKey(t)?i:i.value:isObject(i)?s?readonly(i):reactive(i):i)}}class MutableReactiveHandler extends BaseReactiveHandler{constructor(e=!1){super(!1,e)}set(e,t,n,s){let r=e[t];if(!this._isShallow){const t=isReadonly(r);if(isShallow(n)||isReadonly(n)||(r=toRaw(r),n=toRaw(n)),!isArray(e)&&isRef(r)&&!isRef(n))return!t&&(r.value=n,!0)}const o=isArray(e)&&isIntegerKey(t)?Number(t)<e.length:hasOwn(e,t),i=Reflect.set(e,t,n,isRef(e)?e:s);return e===toRaw(s)&&(o?hasChanged(n,r)&&trigger(e,"set",t,n):trigger(e,"add",t,n)),i}deleteProperty(e,t){const n=hasOwn(e,t);e[t];const s=Reflect.deleteProperty(e,t);return s&&n&&trigger(e,"delete",t,void 0),s}has(e,t){const n=Reflect.has(e,t);return isSymbol(t)&&builtInSymbols.has(t)||track(e,"has",t),n}ownKeys(e){return track(e,"iterate",isArray(e)?"length":ITERATE_KEY),Reflect.ownKeys(e)}}class ReadonlyReactiveHandler extends BaseReactiveHandler{constructor(e=!1){super(!0,e)}set(e,t){return!0}deleteProperty(e,t){return!0}}const mutableHandlers=new MutableReactiveHandler,readonlyHandlers=new ReadonlyReactiveHandler,shallowReactiveHandlers=new MutableReactiveHandler(!0),shallowReadonlyHandlers=new ReadonlyReactiveHandler(!0),toShallow=e=>e,getProto=e=>Reflect.getPrototypeOf(e);function createIterableMethod(e,t,n){return function(...s){const r=this.__v_raw,o=toRaw(r),i=isMap(o),a="entries"===e||e===Symbol.iterator&&i,c="keys"===e&&i,l=r[e](...s),d=n?toShallow:t?toReadonly:toReactive;return!t&&track(o,"iterate",c?MAP_KEY_ITERATE_KEY:ITERATE_KEY),{next(){const{value:e,done:t}=l.next();return t?{value:e,done:t}:{value:a?[d(e[0]),d(e[1])]:d(e),done:t}},[Symbol.iterator](){return this}}}}function createReadonlyMethod(e){return function(...t){return"delete"!==e&&("clear"===e?void 0:this)}}function createInstrumentations(e,t){const n={get(n){const s=this.__v_raw,r=toRaw(s),o=toRaw(n);e||(hasChanged(n,o)&&track(r,"get",n),track(r,"get",o));const{has:i}=getProto(r),a=t?toShallow:e?toReadonly:toReactive;return i.call(r,n)?a(s.get(n)):i.call(r,o)?a(s.get(o)):void(s!==r&&s.get(n))},get size(){const t=this.__v_raw;return!e&&track(toRaw(t),"iterate",ITERATE_KEY),Reflect.get(t,"size",t)},has(t){const n=this.__v_raw,s=toRaw(n),r=toRaw(t);return e||(hasChanged(t,r)&&track(s,"has",t),track(s,"has",r)),t===r?n.has(t):n.has(t)||n.has(r)},forEach(n,s){const r=this,o=r.__v_raw,i=toRaw(o),a=t?toShallow:e?toReadonly:toReactive;return!e&&track(i,"iterate",ITERATE_KEY),o.forEach((e,t)=>n.call(s,a(e),a(t),r))}};extend(n,e?{add:createReadonlyMethod("add"),set:createReadonlyMethod("set"),delete:createReadonlyMethod("delete"),clear:createReadonlyMethod("clear")}:{add(e){t||isShallow(e)||isReadonly(e)||(e=toRaw(e));const n=toRaw(this);return getProto(n).has.call(n,e)||(n.add(e),trigger(n,"add",e,e)),this},set(e,n){t||isShallow(n)||isReadonly(n)||(n=toRaw(n));const s=toRaw(this),{has:r,get:o}=getProto(s);let i=r.call(s,e);i||(e=toRaw(e),i=r.call(s,e));const a=o.call(s,e);return s.set(e,n),i?hasChanged(n,a)&&trigger(s,"set",e,n):trigger(s,"add",e,n),this},delete(e){const t=toRaw(this),{has:n,get:s}=getProto(t);let r=n.call(t,e);r||(e=toRaw(e),r=n.call(t,e)),s&&s.call(t,e);const o=t.delete(e);return r&&trigger(t,"delete",e,void 0),o},clear(){const e=toRaw(this),t=0!==e.size,n=e.clear();return t&&trigger(e,"clear",void 0,void 0),n}});return["keys","values","entries",Symbol.iterator].forEach(s=>{n[s]=createIterableMethod(s,e,t)}),n}function createInstrumentationGetter(e,t){const n=createInstrumentations(e,t);return(t,s,r)=>"__v_isReactive"===s?!e:"__v_isReadonly"===s?e:"__v_raw"===s?t:Reflect.get(hasOwn(n,s)&&s in t?n:t,s,r)}const mutableCollectionHandlers={get:createInstrumentationGetter(!1,!1)},shallowCollectionHandlers={get:createInstrumentationGetter(!1,!0)},readonlyCollectionHandlers={get:createInstrumentationGetter(!0,!1)},shallowReadonlyCollectionHandlers={get:createInstrumentationGetter(!0,!0)},reactiveMap=new WeakMap,shallowReactiveMap=new WeakMap,readonlyMap=new WeakMap,shallowReadonlyMap=new WeakMap;function targetTypeMap(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function getTargetType(e){return e.__v_skip||!Object.isExtensible(e)?0:targetTypeMap(toRawType(e))}function reactive(e){return isReadonly(e)?e:createReactiveObject(e,!1,mutableHandlers,mutableCollectionHandlers,reactiveMap)}function shallowReactive(e){return createReactiveObject(e,!1,shallowReactiveHandlers,shallowCollectionHandlers,shallowReactiveMap)}function readonly(e){return createReactiveObject(e,!0,readonlyHandlers,readonlyCollectionHandlers,readonlyMap)}function shallowReadonly(e){return createReactiveObject(e,!0,shallowReadonlyHandlers,shallowReadonlyCollectionHandlers,shallowReadonlyMap)}function createReactiveObject(e,t,n,s,r){if(!isObject(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const o=getTargetType(e);if(0===o)return e;const i=r.get(e);if(i)return i;const a=new Proxy(e,2===o?s:n);return r.set(e,a),a}function isReactive(e){return isReadonly(e)?isReactive(e.__v_raw):!(!e||!e.__v_isReactive)}function isReadonly(e){return!(!e||!e.__v_isReadonly)}function isShallow(e){return!(!e||!e.__v_isShallow)}function isProxy(e){return!!e&&!!e.__v_raw}function toRaw(e){const t=e&&e.__v_raw;return t?toRaw(t):e}function markRaw(e){return!hasOwn(e,"__v_skip")&&Object.isExtensible(e)&&def(e,"__v_skip",!0),e}const toReactive=e=>isObject(e)?reactive(e):e,toReadonly=e=>isObject(e)?readonly(e):e;function isRef(e){return!!e&&!0===e.__v_isRef}function ref(e){return createRef(e,!1)}function shallowRef(e){return createRef(e,!0)}function createRef(e,t){return isRef(e)?e:new RefImpl(e,t)}class RefImpl{constructor(e,t){this.dep=new Dep,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=t?e:toRaw(e),this._value=t?e:toReactive(e),this.__v_isShallow=t}get value(){return this.dep.track(),this._value}set value(e){const t=this._rawValue,n=this.__v_isShallow||isShallow(e)||isReadonly(e);e=n?e:toRaw(e),hasChanged(e,t)&&(this._rawValue=e,this._value=n?e:toReactive(e),this.dep.trigger())}}function triggerRef(e){e.dep&&e.dep.trigger()}function unref(e){return isRef(e)?e.value:e}function toValue(e){return isFunction(e)?e():unref(e)}const shallowUnwrapHandlers={get:(e,t,n)=>"__v_raw"===t?e:unref(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return isRef(r)&&!isRef(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function proxyRefs(e){return isReactive(e)?e:new Proxy(e,shallowUnwrapHandlers)}class CustomRefImpl{constructor(e){this.__v_isRef=!0,this._value=void 0;const t=this.dep=new Dep,{get:n,set:s}=e(t.track.bind(t),t.trigger.bind(t));this._get=n,this._set=s}get value(){return this._value=this._get()}set value(e){this._set(e)}}function customRef(e){return new CustomRefImpl(e)}function toRefs(e){const t=isArray(e)?new Array(e.length):{};for(const n in e)t[n]=propertyToRef(e,n);return t}class ObjectRefImpl{constructor(e,t,n){this._object=e,this._key=t,this._defaultValue=n,this.__v_isRef=!0,this._value=void 0}get value(){const e=this._object[this._key];return this._value=void 0===e?this._defaultValue:e}set value(e){this._object[this._key]=e}get dep(){return getDepFromReactive(toRaw(this._object),this._key)}}class GetterRefImpl{constructor(e){this._getter=e,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function toRef(e,t,n){return isRef(e)?e:isFunction(e)?new GetterRefImpl(e):isObject(e)&&arguments.length>1?propertyToRef(e,t,n):ref(e)}function propertyToRef(e,t,n){const s=e[t];return isRef(s)?s:new ObjectRefImpl(e,t,n)}class ComputedRefImpl{constructor(e,t,n){this.fn=e,this.setter=t,this._value=void 0,this.dep=new Dep(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=globalVersion-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!t,this.isSSR=n}notify(){if(this.flags|=16,!(8&this.flags)&&activeSub!==this)return batch(this,!0),!0}get value(){const e=this.dep.track();return refreshComputed(this),e&&(e.version=this.dep.version),this._value}set value(e){this.setter&&this.setter(e)}}function computed$1(e,t,n=!1){let s,r;isFunction(e)?s=e:(s=e.get,r=e.set);return new ComputedRefImpl(s,r,n)}const TrackOpTypes={GET:"get",HAS:"has",ITERATE:"iterate"},TriggerOpTypes={SET:"set",ADD:"add",DELETE:"delete",CLEAR:"clear"},INITIAL_WATCHER_VALUE={},cleanupMap=new WeakMap;let activeWatcher;function getCurrentWatcher(){return activeWatcher}function onWatcherCleanup(e,t=!1,n=activeWatcher){if(n){let t=cleanupMap.get(n);t||cleanupMap.set(n,t=[]),t.push(e)}}function watch$1(e,t,n=EMPTY_OBJ){const{immediate:s,deep:r,once:o,scheduler:i,augmentJob:a,call:c}=n,l=e=>r?e:isShallow(e)||!1===r||0===r?traverse(e,1):traverse(e);let d,u,p,h,f=!1,m=!1;if(isRef(e)?(u=()=>e.value,f=isShallow(e)):isReactive(e)?(u=()=>l(e),f=!0):isArray(e)?(m=!0,f=e.some(e=>isReactive(e)||isShallow(e)),u=()=>e.map(e=>isRef(e)?e.value:isReactive(e)?l(e):isFunction(e)?c?c(e,2):e():void 0)):u=isFunction(e)?t?c?()=>c(e,2):e:()=>{if(p){pauseTracking();try{p()}finally{resetTracking()}}const t=activeWatcher;activeWatcher=d;try{return c?c(e,3,[h]):e(h)}finally{activeWatcher=t}}:NOOP,t&&r){const e=u,t=!0===r?1/0:r;u=()=>traverse(e(),t)}const g=getCurrentScope(),y=()=>{d.stop(),g&&g.active&&remove(g.effects,d)};if(o&&t){const e=t;t=(...t)=>{e(...t),y()}}let v=m?new Array(e.length).fill(INITIAL_WATCHER_VALUE):INITIAL_WATCHER_VALUE;const S=e=>{if(1&d.flags&&(d.dirty||e))if(t){const e=d.run();if(r||f||(m?e.some((e,t)=>hasChanged(e,v[t])):hasChanged(e,v))){p&&p();const n=activeWatcher;activeWatcher=d;try{const n=[e,v===INITIAL_WATCHER_VALUE?void 0:m&&v[0]===INITIAL_WATCHER_VALUE?[]:v,h];v=e,c?c(t,3,n):t(...n)}finally{activeWatcher=n}}}else d.run()};return a&&a(S),d=new ReactiveEffect(u),d.scheduler=i?()=>i(S,!1):S,h=e=>onWatcherCleanup(e,!1,d),p=d.onStop=()=>{const e=cleanupMap.get(d);if(e){if(c)c(e,4);else for(const t of e)t();cleanupMap.delete(d)}},t?s?S(!0):v=d.run():i?i(S.bind(null,!0),!0):d.run(),y.pause=d.pause.bind(d),y.resume=d.resume.bind(d),y.stop=y,y}function traverse(e,t=1/0,n){if(t<=0||!isObject(e)||e.__v_skip)return e;if((n=n||new Set).has(e))return e;if(n.add(e),t--,isRef(e))traverse(e.value,t,n);else if(isArray(e))for(let s=0;s<e.length;s++)traverse(e[s],t,n);else if(isSet(e)||isMap(e))e.forEach(e=>{traverse(e,t,n)});else if(isPlainObject(e)){for(const s in e)traverse(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&traverse(e[s],t,n)}return e}
/**
* @vue/runtime-core v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const stack$1=[];function pushWarningContext(e){stack$1.push(e)}function popWarningContext(){stack$1.pop()}let isWarning=!1;function warn$1(e,...t){if(isWarning)return;isWarning=!0,pauseTracking();const n=stack$1.length?stack$1[stack$1.length-1].component:null,s=n&&n.appContext.config.warnHandler,r=getComponentTrace();if(s)callWithErrorHandling(s,n,11,[e+t.map(e=>{var t,n;return null!=(n=null==(t=e.toString)?void 0:t.call(e))?n:JSON.stringify(e)}).join(""),n&&n.proxy,r.map(({vnode:e})=>`at <${formatComponentName(n,e.type)}>`).join("\n"),r]);else{const n=[`[Vue warn]: ${e}`,...t];r.length&&n.push("\n",...formatTrace(r)),console.warn(...n)}resetTracking(),isWarning=!1}function getComponentTrace(){let e=stack$1[stack$1.length-1];if(!e)return[];const t=[];for(;e;){const n=t[0];n&&n.vnode===e?n.recurseCount++:t.push({vnode:e,recurseCount:0});const s=e.component&&e.component.parent;e=s&&s.vnode}return t}function formatTrace(e){const t=[];return e.forEach((e,n)=>{t.push(...0===n?[]:["\n"],...formatTraceEntry(e))}),t}function formatTraceEntry({vnode:e,recurseCount:t}){const n=t>0?`... (${t} recursive calls)`:"",s=!!e.component&&null==e.component.parent,r=` at <${formatComponentName(e.component,e.type,s)}`,o=">"+n;return e.props?[r,...formatProps(e.props),o]:[r+o]}function formatProps(e){const t=[],n=Object.keys(e);return n.slice(0,3).forEach(n=>{t.push(...formatProp(n,e[n]))}),n.length>3&&t.push(" ..."),t}function formatProp(e,t,n){return isString(t)?(t=JSON.stringify(t),n?t:[`${e}=${t}`]):"number"==typeof t||"boolean"==typeof t||null==t?n?t:[`${e}=${t}`]:isRef(t)?(t=formatProp(e,toRaw(t.value),!0),n?t:[`${e}=Ref<`,t,">"]):isFunction(t)?[`${e}=fn${t.name?`<${t.name}>`:""}`]:(t=toRaw(t),n?t:[`${e}=`,t])}function assertNumber(e,t){}const ErrorCodes={SETUP_FUNCTION:0,0:"SETUP_FUNCTION",RENDER_FUNCTION:1,1:"RENDER_FUNCTION",NATIVE_EVENT_HANDLER:5,5:"NATIVE_EVENT_HANDLER",COMPONENT_EVENT_HANDLER:6,6:"COMPONENT_EVENT_HANDLER",VNODE_HOOK:7,7:"VNODE_HOOK",DIRECTIVE_HOOK:8,8:"DIRECTIVE_HOOK",TRANSITION_HOOK:9,9:"TRANSITION_HOOK",APP_ERROR_HANDLER:10,10:"APP_ERROR_HANDLER",APP_WARN_HANDLER:11,11:"APP_WARN_HANDLER",FUNCTION_REF:12,12:"FUNCTION_REF",ASYNC_COMPONENT_LOADER:13,13:"ASYNC_COMPONENT_LOADER",SCHEDULER:14,14:"SCHEDULER",COMPONENT_UPDATE:15,15:"COMPONENT_UPDATE",APP_UNMOUNT_CLEANUP:16,16:"APP_UNMOUNT_CLEANUP"},ErrorTypeStrings$1={sp:"serverPrefetch hook",bc:"beforeCreate hook",c:"created hook",bm:"beforeMount hook",m:"mounted hook",bu:"beforeUpdate hook",u:"updated",bum:"beforeUnmount hook",um:"unmounted hook",a:"activated hook",da:"deactivated hook",ec:"errorCaptured hook",rtc:"renderTracked hook",rtg:"renderTriggered hook",0:"setup function",1:"render function",2:"watcher getter",3:"watcher callback",4:"watcher cleanup function",5:"native event handler",6:"component event handler",7:"vnode hook",8:"directive hook",9:"transition hook",10:"app errorHandler",11:"app warnHandler",12:"ref function",13:"async component loader",14:"scheduler flush",15:"component update",16:"app unmount cleanup function"};function callWithErrorHandling(e,t,n,s){try{return s?e(...s):e()}catch(r){handleError(r,t,n)}}function callWithAsyncErrorHandling(e,t,n,s){if(isFunction(e)){const r=callWithErrorHandling(e,t,n,s);return r&&isPromise(r)&&r.catch(e=>{handleError(e,t,n)}),r}if(isArray(e)){const r=[];for(let o=0;o<e.length;o++)r.push(callWithAsyncErrorHandling(e[o],t,n,s));return r}}function handleError(e,t,n,s=!0){const r=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||EMPTY_OBJ;if(t){let s=t.parent;const r=t.proxy,i=`https://vuejs.org/error-reference/#runtime-${n}`;for(;s;){const t=s.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,r,i))return;s=s.parent}if(o)return pauseTracking(),callWithErrorHandling(o,null,10,[e,r,i]),void resetTracking()}logError(e,n,r,s,i)}function logError(e,t,n,s=!0,r=!1){if(r)throw e;console.error(e)}const queue=[];let flushIndex=-1;const pendingPostFlushCbs=[];let activePostFlushCbs=null,postFlushIndex=0;const resolvedPromise=Promise.resolve();let currentFlushPromise=null;function nextTick(e){const t=currentFlushPromise||resolvedPromise;return e?t.then(this?e.bind(this):e):t}function findInsertionIndex(e){let t=flushIndex+1,n=queue.length;for(;t<n;){const s=t+n>>>1,r=queue[s],o=getId(r);o<e||o===e&&2&r.flags?t=s+1:n=s}return t}function queueJob(e){if(!(1&e.flags)){const t=getId(e),n=queue[queue.length-1];!n||!(2&e.flags)&&t>=getId(n)?queue.push(e):queue.splice(findInsertionIndex(t),0,e),e.flags|=1,queueFlush()}}function queueFlush(){currentFlushPromise||(currentFlushPromise=resolvedPromise.then(flushJobs))}function queuePostFlushCb(e){isArray(e)?pendingPostFlushCbs.push(...e):activePostFlushCbs&&-1===e.id?activePostFlushCbs.splice(postFlushIndex+1,0,e):1&e.flags||(pendingPostFlushCbs.push(e),e.flags|=1),queueFlush()}function flushPreFlushCbs(e,t,n=flushIndex+1){for(;n<queue.length;n++){const t=queue[n];if(t&&2&t.flags){if(e&&t.id!==e.uid)continue;queue.splice(n,1),n--,4&t.flags&&(t.flags&=-2),t(),4&t.flags||(t.flags&=-2)}}}function flushPostFlushCbs(e){if(pendingPostFlushCbs.length){const e=[...new Set(pendingPostFlushCbs)].sort((e,t)=>getId(e)-getId(t));if(pendingPostFlushCbs.length=0,activePostFlushCbs)return void activePostFlushCbs.push(...e);for(activePostFlushCbs=e,postFlushIndex=0;postFlushIndex<activePostFlushCbs.length;postFlushIndex++){const e=activePostFlushCbs[postFlushIndex];4&e.flags&&(e.flags&=-2),8&e.flags||e(),e.flags&=-2}activePostFlushCbs=null,postFlushIndex=0}}const getId=e=>null==e.id?2&e.flags?-1:1/0:e.id;function flushJobs(e){try{for(flushIndex=0;flushIndex<queue.length;flushIndex++){const e=queue[flushIndex];!e||8&e.flags||(4&e.flags&&(e.flags&=-2),callWithErrorHandling(e,e.i,e.i?15:14),4&e.flags||(e.flags&=-2))}}finally{for(;flushIndex<queue.length;flushIndex++){const e=queue[flushIndex];e&&(e.flags&=-2)}flushIndex=-1,queue.length=0,flushPostFlushCbs(),currentFlushPromise=null,(queue.length||pendingPostFlushCbs.length)&&flushJobs()}}let devtools$1,buffer=[];function setDevtoolsHook$1(e,t){var n,s;if(devtools$1=e,devtools$1)devtools$1.enabled=!0,buffer.forEach(({event:e,args:t})=>devtools$1.emit(e,...t)),buffer=[];else if("undefined"!=typeof window&&window.HTMLElement&&!(null==(s=null==(n=window.navigator)?void 0:n.userAgent)?void 0:s.includes("jsdom"))){(t.__VUE_DEVTOOLS_HOOK_REPLAY__=t.__VUE_DEVTOOLS_HOOK_REPLAY__||[]).push(e=>{setDevtoolsHook$1(e,t)}),setTimeout(()=>{devtools$1||(t.__VUE_DEVTOOLS_HOOK_REPLAY__=null,buffer=[])},3e3)}else buffer=[]}let currentRenderingInstance=null,currentScopeId=null;function setCurrentRenderingInstance(e){const t=currentRenderingInstance;return currentRenderingInstance=e,currentScopeId=e&&e.type.__scopeId||null,t}function pushScopeId(e){currentScopeId=e}function popScopeId(){currentScopeId=null}const withScopeId=e=>withCtx;function withCtx(e,t=currentRenderingInstance,n){if(!t)return e;if(e._n)return e;const s=(...n)=>{s._d&&setBlockTracking(-1);const r=setCurrentRenderingInstance(t);let o;try{o=e(...n)}finally{setCurrentRenderingInstance(r),s._d&&setBlockTracking(1)}return o};return s._n=!0,s._c=!0,s._d=!0,s}function withDirectives(e,t){if(null===currentRenderingInstance)return e;const n=getComponentPublicInstance(currentRenderingInstance),s=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[e,o,i,a=EMPTY_OBJ]=t[r];e&&(isFunction(e)&&(e={mounted:e,updated:e}),e.deep&&traverse(o),s.push({dir:e,instance:n,value:o,oldValue:void 0,arg:i,modifiers:a}))}return e}function invokeDirectiveHook(e,t,n,s){const r=e.dirs,o=t&&t.dirs;for(let i=0;i<r.length;i++){const a=r[i];o&&(a.oldValue=o[i].value);let c=a.dir[s];c&&(pauseTracking(),callWithAsyncErrorHandling(c,n,8,[e.el,a,e,t]),resetTracking())}}const TeleportEndKey=Symbol("_vte"),isTeleport=e=>e.__isTeleport,isTeleportDisabled=e=>e&&(e.disabled||""===e.disabled),isTeleportDeferred=e=>e&&(e.defer||""===e.defer),isTargetSVG=e=>"undefined"!=typeof SVGElement&&e instanceof SVGElement,isTargetMathML=e=>"function"==typeof MathMLElement&&e instanceof MathMLElement,resolveTarget=(e,t)=>{const n=e&&e.to;if(isString(n)){if(t){return t(n)}return null}return n},TeleportImpl={name:"Teleport",__isTeleport:!0,process(e,t,n,s,r,o,i,a,c,l){const{mc:d,pc:u,pbc:p,o:{insert:h,querySelector:f,createText:m,createComment:g}}=l,y=isTeleportDisabled(t.props);let{shapeFlag:v,children:S,dynamicChildren:_}=t;if(null==e){const e=t.el=m(""),l=t.anchor=m("");h(e,n,s),h(l,n,s);const u=(e,t)=>{16&v&&(r&&r.isCE&&(r.ce._teleportTarget=e),d(S,e,t,r,o,i,a,c))},p=()=>{const e=t.target=resolveTarget(t.props,f),n=prepareAnchor(e,t,m,h);e&&("svg"!==i&&isTargetSVG(e)?i="svg":"mathml"!==i&&isTargetMathML(e)&&(i="mathml"),y||(u(e,n),updateCssVars(t,!1)))};y&&(u(n,l),updateCssVars(t,!0)),isTeleportDeferred(t.props)?(t.el.__isMounted=!1,queuePostRenderEffect(()=>{p(),delete t.el.__isMounted},o)):p()}else{if(isTeleportDeferred(t.props)&&!1===e.el.__isMounted)return void queuePostRenderEffect(()=>{TeleportImpl.process(e,t,n,s,r,o,i,a,c,l)},o);t.el=e.el,t.targetStart=e.targetStart;const d=t.anchor=e.anchor,h=t.target=e.target,m=t.targetAnchor=e.targetAnchor,g=isTeleportDisabled(e.props),v=g?n:h,S=g?d:m;if("svg"===i||isTargetSVG(h)?i="svg":("mathml"===i||isTargetMathML(h))&&(i="mathml"),_?(p(e.dynamicChildren,_,v,r,o,i,a),traverseStaticChildren(e,t,!0)):c||u(e,t,v,S,r,o,i,a,!1),y)g?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):moveTeleport(t,n,d,l,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const e=t.target=resolveTarget(t.props,f);e&&moveTeleport(t,e,null,l,0)}else g&&moveTeleport(t,h,m,l,1);updateCssVars(t,y)}},remove(e,t,n,{um:s,o:{remove:r}},o){const{shapeFlag:i,children:a,anchor:c,targetStart:l,targetAnchor:d,target:u,props:p}=e;if(u&&(r(l),r(d)),o&&r(c),16&i){const e=o||!isTeleportDisabled(p);for(let r=0;r<a.length;r++){const o=a[r];s(o,t,n,e,!!o.dynamicChildren)}}},move:moveTeleport,hydrate:hydrateTeleport};function moveTeleport(e,t,n,{o:{insert:s},m:r},o=2){0===o&&s(e.targetAnchor,t,n);const{el:i,anchor:a,shapeFlag:c,children:l,props:d}=e,u=2===o;if(u&&s(i,t,n),(!u||isTeleportDisabled(d))&&16&c)for(let p=0;p<l.length;p++)r(l[p],t,n,2);u&&s(a,t,n)}function hydrateTeleport(e,t,n,s,r,o,{o:{nextSibling:i,parentNode:a,querySelector:c,insert:l,createText:d}},u){const p=t.target=resolveTarget(t.props,c);if(p){const c=isTeleportDisabled(t.props),h=p._lpa||p.firstChild;if(16&t.shapeFlag)if(c)t.anchor=u(i(e),t,a(e),n,s,r,o),t.targetStart=h,t.targetAnchor=h&&i(h);else{t.anchor=i(e);let a=h;for(;a;){if(a&&8===a.nodeType)if("teleport start anchor"===a.data)t.targetStart=a;else if("teleport anchor"===a.data){t.targetAnchor=a,p._lpa=t.targetAnchor&&i(t.targetAnchor);break}a=i(a)}t.targetAnchor||prepareAnchor(p,t,d,l),u(h&&i(h),t,p,n,s,r,o)}updateCssVars(t,c)}return t.anchor&&i(t.anchor)}const Teleport=TeleportImpl;function updateCssVars(e,t){const n=e.ctx;if(n&&n.ut){let s,r;for(t?(s=e.el,r=e.anchor):(s=e.targetStart,r=e.targetAnchor);s&&s!==r;)1===s.nodeType&&s.setAttribute("data-v-owner",n.uid),s=s.nextSibling;n.ut()}}function prepareAnchor(e,t,n,s){const r=t.targetStart=n(""),o=t.targetAnchor=n("");return r[TeleportEndKey]=o,e&&(s(r,e),s(o,e)),o}const leaveCbKey=Symbol("_leaveCb"),enterCbKey$1=Symbol("_enterCb");function useTransitionState(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return onMounted(()=>{e.isMounted=!0}),onBeforeUnmount(()=>{e.isUnmounting=!0}),e}const TransitionHookValidator=[Function,Array],BaseTransitionPropsValidators={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:TransitionHookValidator,onEnter:TransitionHookValidator,onAfterEnter:TransitionHookValidator,onEnterCancelled:TransitionHookValidator,onBeforeLeave:TransitionHookValidator,onLeave:TransitionHookValidator,onAfterLeave:TransitionHookValidator,onLeaveCancelled:TransitionHookValidator,onBeforeAppear:TransitionHookValidator,onAppear:TransitionHookValidator,onAfterAppear:TransitionHookValidator,onAppearCancelled:TransitionHookValidator},recursiveGetSubtree=e=>{const t=e.subTree;return t.component?recursiveGetSubtree(t.component):t},BaseTransitionImpl={name:"BaseTransition",props:BaseTransitionPropsValidators,setup(e,{slots:t}){const n=getCurrentInstance(),s=useTransitionState();return()=>{const r=t.default&&getTransitionRawChildren(t.default(),!0);if(!r||!r.length)return;const o=findNonCommentChild(r),i=toRaw(e),{mode:a}=i;if(s.isLeaving)return emptyPlaceholder(o);const c=getInnerChild$1(o);if(!c)return emptyPlaceholder(o);let l=resolveTransitionHooks(c,i,s,n,e=>l=e);c.type!==Comment&&setTransitionHooks(c,l);let d=n.subTree&&getInnerChild$1(n.subTree);if(d&&d.type!==Comment&&!isSameVNodeType(c,d)&&recursiveGetSubtree(n).type!==Comment){let e=resolveTransitionHooks(d,i,s,n);if(setTransitionHooks(d,e),"out-in"===a&&c.type!==Comment)return s.isLeaving=!0,e.afterLeave=()=>{s.isLeaving=!1,8&n.job.flags||n.update(),delete e.afterLeave,d=void 0},emptyPlaceholder(o);"in-out"===a&&c.type!==Comment?e.delayLeave=(e,t,n)=>{getLeavingNodesForType(s,d)[String(d.key)]=d,e[leaveCbKey]=()=>{t(),e[leaveCbKey]=void 0,delete l.delayedLeave,d=void 0},l.delayedLeave=()=>{n(),delete l.delayedLeave,d=void 0}}:d=void 0}else d&&(d=void 0);return o}}};function findNonCommentChild(e){let t=e[0];if(e.length>1)for(const n of e)if(n.type!==Comment){t=n;break}return t}const BaseTransition=BaseTransitionImpl;function getLeavingNodesForType(e,t){const{leavingVNodes:n}=e;let s=n.get(t.type);return s||(s=Object.create(null),n.set(t.type,s)),s}function resolveTransitionHooks(e,t,n,s,r){const{appear:o,mode:i,persisted:a=!1,onBeforeEnter:c,onEnter:l,onAfterEnter:d,onEnterCancelled:u,onBeforeLeave:p,onLeave:h,onAfterLeave:f,onLeaveCancelled:m,onBeforeAppear:g,onAppear:y,onAfterAppear:v,onAppearCancelled:S}=t,_=String(e.key),b=getLeavingNodesForType(n,e),E=(e,t)=>{e&&callWithAsyncErrorHandling(e,s,9,t)},C=(e,t)=>{const n=t[1];E(e,t),isArray(e)?e.every(e=>e.length<=1)&&n():e.length<=1&&n()},T={mode:i,persisted:a,beforeEnter(t){let s=c;if(!n.isMounted){if(!o)return;s=g||c}t[leaveCbKey]&&t[leaveCbKey](!0);const r=b[_];r&&isSameVNodeType(e,r)&&r.el[leaveCbKey]&&r.el[leaveCbKey](),E(s,[t])},enter(e){let t=l,s=d,r=u;if(!n.isMounted){if(!o)return;t=y||l,s=v||d,r=S||u}let i=!1;const a=e[enterCbKey$1]=t=>{i||(i=!0,E(t?r:s,[e]),T.delayedLeave&&T.delayedLeave(),e[enterCbKey$1]=void 0)};t?C(t,[e,a]):a()},leave(t,s){const r=String(e.key);if(t[enterCbKey$1]&&t[enterCbKey$1](!0),n.isUnmounting)return s();E(p,[t]);let o=!1;const i=t[leaveCbKey]=n=>{o||(o=!0,s(),E(n?m:f,[t]),t[leaveCbKey]=void 0,b[r]===e&&delete b[r])};b[r]=e,h?C(h,[t,i]):i()},clone(e){const o=resolveTransitionHooks(e,t,n,s,r);return r&&r(o),o}};return T}function emptyPlaceholder(e){if(isKeepAlive(e))return(e=cloneVNode(e)).children=null,e}function getInnerChild$1(e){if(!isKeepAlive(e))return isTeleport(e.type)&&e.children?findNonCommentChild(e.children):e;if(e.component)return e.component.subTree;const{shapeFlag:t,children:n}=e;if(n){if(16&t)return n[0];if(32&t&&isFunction(n.default))return n.default()}}function setTransitionHooks(e,t){6&e.shapeFlag&&e.component?(e.transition=t,setTransitionHooks(e.component.subTree,t)):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function getTransitionRawChildren(e,t=!1,n){let s=[],r=0;for(let o=0;o<e.length;o++){let i=e[o];const a=null==n?i.key:String(n)+String(null!=i.key?i.key:o);i.type===Fragment?(128&i.patchFlag&&r++,s=s.concat(getTransitionRawChildren(i.children,t,a))):(t||i.type!==Comment)&&s.push(null!=a?cloneVNode(i,{key:a}):i)}if(r>1)for(let o=0;o<s.length;o++)s[o].patchFlag=-2;return s}
/*! #__NO_SIDE_EFFECTS__ */function defineComponent(e,t){return isFunction(e)?(()=>extend({name:e.name},t,{setup:e}))():e}function useId(){const e=getCurrentInstance();return e?(e.appContext.config.idPrefix||"v")+"-"+e.ids[0]+e.ids[1]++:""}function markAsyncBoundary(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function useTemplateRef(e){const t=getCurrentInstance(),n=shallowRef(null);if(t){const s=t.refs===EMPTY_OBJ?t.refs={}:t.refs;Object.defineProperty(s,e,{enumerable:!0,get:()=>n.value,set:e=>n.value=e})}return n}function setRef(e,t,n,s,r=!1){if(isArray(e))return void e.forEach((e,o)=>setRef(e,t&&(isArray(t)?t[o]:t),n,s,r));if(isAsyncWrapper(s)&&!r)return void(512&s.shapeFlag&&s.type.__asyncResolved&&s.component.subTree.component&&setRef(e,t,n,s.component.subTree));const o=4&s.shapeFlag?getComponentPublicInstance(s.component):s.el,i=r?null:o,{i:a,r:c}=e,l=t&&t.r,d=a.refs===EMPTY_OBJ?a.refs={}:a.refs,u=a.setupState,p=toRaw(u),h=u===EMPTY_OBJ?()=>!1:e=>hasOwn(p,e);if(null!=l&&l!==c&&(isString(l)?(d[l]=null,h(l)&&(u[l]=null)):isRef(l)&&(l.value=null)),isFunction(c))callWithErrorHandling(c,a,12,[i,d]);else{const t=isString(c),s=isRef(c);if(t||s){const a=()=>{if(e.f){const n=t?h(c)?u[c]:d[c]:c.value;r?isArray(n)&&remove(n,o):isArray(n)?n.includes(o)||n.push(o):t?(d[c]=[o],h(c)&&(u[c]=d[c])):(c.value=[o],e.k&&(d[e.k]=c.value))}else t?(d[c]=i,h(c)&&(u[c]=i)):s&&(c.value=i,e.k&&(d[e.k]=i))};i?(a.id=-1,queuePostRenderEffect(a,n)):a()}}}let hasLoggedMismatchError=!1;const logMismatchError=()=>{hasLoggedMismatchError||(console.error("Hydration completed but contains mismatches."),hasLoggedMismatchError=!0)},isSVGContainer=e=>e.namespaceURI.includes("svg")&&"foreignObject"!==e.tagName,isMathMLContainer=e=>e.namespaceURI.includes("MathML"),getContainerType=e=>{if(1===e.nodeType)return isSVGContainer(e)?"svg":isMathMLContainer(e)?"mathml":void 0},isComment=e=>8===e.nodeType;function createHydrationFunctions(e){const{mt:t,p:n,o:{patchProp:s,createText:r,nextSibling:o,parentNode:i,remove:a,insert:c,createComment:l}}=e,d=(n,s,a,l,v,S=!1)=>{S=S||!!s.dynamicChildren;const _=isComment(n)&&"["===n.data,b=()=>f(n,s,a,l,v,_),{type:E,ref:C,shapeFlag:T,patchFlag:N}=s;let A=n.nodeType;s.el=n,-2===N&&(S=!1,s.dynamicChildren=null);let R=null;switch(E){case Text:3!==A?""===s.children?(c(s.el=r(""),i(n),n),R=n):R=b():(n.data!==s.children&&(logMismatchError(),n.data=s.children),R=o(n));break;case Comment:y(n)?(R=o(n),g(s.el=n.content.firstChild,n,a)):R=8!==A||_?b():o(n);break;case Static:if(_&&(A=(n=o(n)).nodeType),1===A||3===A){R=n;const e=!s.children.length;for(let t=0;t<s.staticCount;t++)e&&(s.children+=1===R.nodeType?R.outerHTML:R.data),t===s.staticCount-1&&(s.anchor=R),R=o(R);return _?o(R):R}b();break;case Fragment:R=_?h(n,s,a,l,v,S):b();break;default:if(1&T)R=1===A&&s.type.toLowerCase()===n.tagName.toLowerCase()||y(n)?u(n,s,a,l,v,S):b();else if(6&T){s.slotScopeIds=v;const e=i(n);if(R=_?m(n):isComment(n)&&"teleport start"===n.data?m(n,n.data,"teleport end"):o(n),t(s,e,null,a,l,getContainerType(e),S),isAsyncWrapper(s)&&!s.type.__asyncResolved){let t;_?(t=createVNode(Fragment),t.anchor=R?R.previousSibling:e.lastChild):t=3===n.nodeType?createTextVNode(""):createVNode("div"),t.el=n,s.component.subTree=t}}else 64&T?R=8!==A?b():s.type.hydrate(n,s,a,l,v,S,e,p):128&T&&(R=s.type.hydrate(n,s,a,l,getContainerType(i(n)),v,S,e,d))}return null!=C&&setRef(C,null,l,s),R},u=(e,t,n,r,o,i)=>{i=i||!!t.dynamicChildren;const{type:c,props:l,patchFlag:d,shapeFlag:u,dirs:h,transition:f}=t,m="input"===c||"option"===c;if(m||-1!==d){h&&invokeDirectiveHook(t,null,n,"created");let c,v=!1;if(y(e)){v=needTransition(null,f)&&n&&n.vnode.props&&n.vnode.props.appear;const s=e.content.firstChild;if(v){const e=s.getAttribute("class");e&&(s.$cls=e),f.beforeEnter(s)}g(s,e,n),t.el=e=s}if(16&u&&(!l||!l.innerHTML&&!l.textContent)){let s=p(e.firstChild,t,e,n,r,o,i);for(;s;){isMismatchAllowed(e,1)||logMismatchError();const t=s;s=s.nextSibling,a(t)}}else if(8&u){let n=t.children;"\n"!==n[0]||"PRE"!==e.tagName&&"TEXTAREA"!==e.tagName||(n=n.slice(1)),e.textContent!==n&&(isMismatchAllowed(e,0)||logMismatchError(),e.textContent=t.children)}if(l)if(m||!i||48&d){const t=e.tagName.includes("-");for(const r in l)(m&&(r.endsWith("value")||"indeterminate"===r)||isOn(r)&&!isReservedProp(r)||"."===r[0]||t)&&s(e,r,null,l[r],void 0,n)}else if(l.onClick)s(e,"onClick",null,l.onClick,void 0,n);else if(4&d&&isReactive(l.style))for(const e in l.style)l.style[e];(c=l&&l.onVnodeBeforeMount)&&invokeVNodeHook(c,n,t),h&&invokeDirectiveHook(t,null,n,"beforeMount"),((c=l&&l.onVnodeMounted)||h||v)&&queueEffectWithSuspense(()=>{c&&invokeVNodeHook(c,n,t),v&&f.enter(e),h&&invokeDirectiveHook(t,null,n,"mounted")},r)}return e.nextSibling},p=(e,t,s,i,a,l,u)=>{u=u||!!t.dynamicChildren;const p=t.children,h=p.length;for(let f=0;f<h;f++){const t=u?p[f]:p[f]=normalizeVNode(p[f]),m=t.type===Text;e?(m&&!u&&f+1<h&&normalizeVNode(p[f+1]).type===Text&&(c(r(e.data.slice(t.children.length)),s,o(e)),e.data=t.children),e=d(e,t,i,a,l,u)):m&&!t.children?c(t.el=r(""),s):(isMismatchAllowed(s,1)||logMismatchError(),n(null,t,s,null,i,a,getContainerType(s),l))}return e},h=(e,t,n,s,r,a)=>{const{slotScopeIds:d}=t;d&&(r=r?r.concat(d):d);const u=i(e),h=p(o(e),t,u,n,s,r,a);return h&&isComment(h)&&"]"===h.data?o(t.anchor=h):(logMismatchError(),c(t.anchor=l("]"),u,h),h)},f=(e,t,s,r,c,l)=>{if(isMismatchAllowed(e.parentElement,1)||logMismatchError(),t.el=null,l){const t=m(e);for(;;){const n=o(e);if(!n||n===t)break;a(n)}}const d=o(e),u=i(e);return a(e),n(null,t,u,d,s,r,getContainerType(u),c),s&&(s.vnode.el=t.el,updateHOCHostEl(s,t.el)),d},m=(e,t="[",n="]")=>{let s=0;for(;e;)if((e=o(e))&&isComment(e)&&(e.data===t&&s++,e.data===n)){if(0===s)return o(e);s--}return e},g=(e,t,n)=>{const s=t.parentNode;s&&s.replaceChild(e,t);let r=n;for(;r;)r.vnode.el===t&&(r.vnode.el=r.subTree.el=e),r=r.parent},y=e=>1===e.nodeType&&"TEMPLATE"===e.tagName;return[(e,t)=>{if(!t.hasChildNodes())return n(null,e,t),flushPostFlushCbs(),void(t._vnode=e);d(t.firstChild,e,null,null,null),flushPostFlushCbs(),t._vnode=e},d]}const allowMismatchAttr="data-allow-mismatch",MismatchTypeString={0:"text",1:"children",2:"class",3:"style",4:"attribute"};function isMismatchAllowed(e,t){if(0===t||1===t)for(;e&&!e.hasAttribute(allowMismatchAttr);)e=e.parentElement;const n=e&&e.getAttribute(allowMismatchAttr);if(null==n)return!1;if(""===n)return!0;{const e=n.split(",");return!(0!==t||!e.includes("children"))||e.includes(MismatchTypeString[t])}}const requestIdleCallback=getGlobalThis().requestIdleCallback||(e=>setTimeout(e,1)),cancelIdleCallback=getGlobalThis().cancelIdleCallback||(e=>clearTimeout(e)),hydrateOnIdle=(e=1e4)=>t=>{const n=requestIdleCallback(t,{timeout:e});return()=>cancelIdleCallback(n)};function elementIsVisibleInViewport(e){const{top:t,left:n,bottom:s,right:r}=e.getBoundingClientRect(),{innerHeight:o,innerWidth:i}=window;return(t>0&&t<o||s>0&&s<o)&&(n>0&&n<i||r>0&&r<i)}const hydrateOnVisible=e=>(t,n)=>{const s=new IntersectionObserver(e=>{for(const n of e)if(n.isIntersecting){s.disconnect(),t();break}},e);return n(e=>{if(e instanceof Element)return elementIsVisibleInViewport(e)?(t(),s.disconnect(),!1):void s.observe(e)}),()=>s.disconnect()},hydrateOnMediaQuery=e=>t=>{if(e){const n=matchMedia(e);if(!n.matches)return n.addEventListener("change",t,{once:!0}),()=>n.removeEventListener("change",t);t()}},hydrateOnInteraction=(e=[])=>(t,n)=>{isString(e)&&(e=[e]);let s=!1;const r=e=>{s||(s=!0,o(),t(),e.target.dispatchEvent(new e.constructor(e.type,e)))},o=()=>{n(t=>{for(const n of e)t.removeEventListener(n,r)})};return n(t=>{for(const n of e)t.addEventListener(n,r,{once:!0})}),o};function forEachElement(e,t){if(isComment(e)&&"["===e.data){let n=1,s=e.nextSibling;for(;s;){if(1===s.nodeType){if(!1===t(s))break}else if(isComment(s))if("]"===s.data){if(0===--n)break}else"["===s.data&&n++;s=s.nextSibling}}else t(e)}const isAsyncWrapper=e=>!!e.type.__asyncLoader;
/*! #__NO_SIDE_EFFECTS__ */function defineAsyncComponent(e){isFunction(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:s,delay:r=200,hydrate:o,timeout:i,suspensible:a=!0,onError:c}=e;let l,d=null,u=0;const p=()=>{let e;return d||(e=d=t().catch(e=>{if(e=e instanceof Error?e:new Error(String(e)),c)return new Promise((t,n)=>{c(e,()=>t((u++,d=null,p())),()=>n(e),u+1)});throw e}).then(t=>e!==d&&d?d:(t&&(t.__esModule||"Module"===t[Symbol.toStringTag])&&(t=t.default),l=t,t)))};return defineComponent({name:"AsyncComponentWrapper",__asyncLoader:p,__asyncHydrate(e,t,n){let s=!1;(t.bu||(t.bu=[])).push(()=>s=!0);const r=()=>{s||n()},i=o?()=>{const n=o(r,t=>forEachElement(e,t));n&&(t.bum||(t.bum=[])).push(n)}:r;l?i():p().then(()=>!t.isUnmounted&&i())},get __asyncResolved(){return l},setup(){const e=currentInstance;if(markAsyncBoundary(e),l)return()=>createInnerComp(l,e);const t=t=>{d=null,handleError(t,e,13,!s)};if(a&&e.suspense||isInSSRComponentSetup)return p().then(t=>()=>createInnerComp(t,e)).catch(e=>(t(e),()=>s?createVNode(s,{error:e}):null));const o=ref(!1),c=ref(),u=ref(!!r);return r&&setTimeout(()=>{u.value=!1},r),null!=i&&setTimeout(()=>{if(!o.value&&!c.value){const e=new Error(`Async component timed out after ${i}ms.`);t(e),c.value=e}},i),p().then(()=>{o.value=!0,e.parent&&isKeepAlive(e.parent.vnode)&&e.parent.update()}).catch(e=>{t(e),c.value=e}),()=>o.value&&l?createInnerComp(l,e):c.value&&s?createVNode(s,{error:c.value}):n&&!u.value?createVNode(n):void 0}})}function createInnerComp(e,t){const{ref:n,props:s,children:r,ce:o}=t.vnode,i=createVNode(e,s,r);return i.ref=n,i.ce=o,delete t.vnode.ce,i}const isKeepAlive=e=>e.type.__isKeepAlive,KeepAliveImpl={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number]},setup(e,{slots:t}){const n=getCurrentInstance(),s=n.ctx;if(!s.renderer)return()=>{const e=t.default&&t.default();return e&&1===e.length?e[0]:e};const r=new Map,o=new Set;let i=null;const a=n.suspense,{renderer:{p:c,m:l,um:d,o:{createElement:u}}}=s,p=u("div");function h(e){resetShapeFlag(e),d(e,n,a,!0)}function f(e){r.forEach((t,n)=>{const s=getComponentName(t.type);s&&!e(s)&&m(n)})}function m(e){const t=r.get(e);!t||i&&isSameVNodeType(t,i)?i&&resetShapeFlag(i):h(t),r.delete(e),o.delete(e)}s.activate=(e,t,n,s,r)=>{const o=e.component;l(e,t,n,0,a),c(o.vnode,e,t,n,o,a,s,e.slotScopeIds,r),queuePostRenderEffect(()=>{o.isDeactivated=!1,o.a&&invokeArrayFns(o.a);const t=e.props&&e.props.onVnodeMounted;t&&invokeVNodeHook(t,o.parent,e)},a)},s.deactivate=e=>{const t=e.component;invalidateMount(t.m),invalidateMount(t.a),l(e,p,null,1,a),queuePostRenderEffect(()=>{t.da&&invokeArrayFns(t.da);const n=e.props&&e.props.onVnodeUnmounted;n&&invokeVNodeHook(n,t.parent,e),t.isDeactivated=!0},a)},watch(()=>[e.include,e.exclude],([e,t])=>{e&&f(t=>matches(e,t)),t&&f(e=>!matches(t,e))},{flush:"post",deep:!0});let g=null;const y=()=>{null!=g&&(isSuspense(n.subTree.type)?queuePostRenderEffect(()=>{r.set(g,getInnerChild(n.subTree))},n.subTree.suspense):r.set(g,getInnerChild(n.subTree)))};return onMounted(y),onUpdated(y),onBeforeUnmount(()=>{r.forEach(e=>{const{subTree:t,suspense:s}=n,r=getInnerChild(t);if(e.type===r.type&&e.key===r.key){resetShapeFlag(r);const e=r.component.da;return void(e&&queuePostRenderEffect(e,s))}h(e)})}),()=>{if(g=null,!t.default)return i=null;const n=t.default(),s=n[0];if(n.length>1)return i=null,n;if(!(isVNode(s)&&(4&s.shapeFlag||128&s.shapeFlag)))return i=null,s;let a=getInnerChild(s);if(a.type===Comment)return i=null,a;const c=a.type,l=getComponentName(isAsyncWrapper(a)?a.type.__asyncResolved||{}:c),{include:d,exclude:u,max:p}=e;if(d&&(!l||!matches(d,l))||u&&l&&matches(u,l))return a.shapeFlag&=-257,i=a,s;const h=null==a.key?c:a.key,f=r.get(h);return a.el&&(a=cloneVNode(a),128&s.shapeFlag&&(s.ssContent=a)),g=h,f?(a.el=f.el,a.component=f.component,a.transition&&setTransitionHooks(a,a.transition),a.shapeFlag|=512,o.delete(h),o.add(h)):(o.add(h),p&&o.size>parseInt(p,10)&&m(o.values().next().value)),a.shapeFlag|=256,i=a,isSuspense(s.type)?s:a}}},KeepAlive=KeepAliveImpl;function matches(e,t){return isArray(e)?e.some(e=>matches(e,t)):isString(e)?e.split(",").includes(t):!!isRegExp(e)&&(e.lastIndex=0,e.test(t))}function onActivated(e,t){registerKeepAliveHook(e,"a",t)}function onDeactivated(e,t){registerKeepAliveHook(e,"da",t)}function registerKeepAliveHook(e,t,n=currentInstance){const s=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(injectHook(t,s,n),n){let e=n.parent;for(;e&&e.parent;)isKeepAlive(e.parent.vnode)&&injectToKeepAliveRoot(s,t,n,e),e=e.parent}}function injectToKeepAliveRoot(e,t,n,s){const r=injectHook(t,e,s,!0);onUnmounted(()=>{remove(s[t],r)},n)}function resetShapeFlag(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function getInnerChild(e){return 128&e.shapeFlag?e.ssContent:e}function injectHook(e,t,n=currentInstance,s=!1){if(n){const r=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...s)=>{pauseTracking();const r=setCurrentInstance(n),o=callWithAsyncErrorHandling(t,n,e,s);return r(),resetTracking(),o});return s?r.unshift(o):r.push(o),o}}const createHook=e=>(t,n=currentInstance)=>{isInSSRComponentSetup&&"sp"!==e||injectHook(e,(...e)=>t(...e),n)},onBeforeMount=createHook("bm"),onMounted=createHook("m"),onBeforeUpdate=createHook("bu"),onUpdated=createHook("u"),onBeforeUnmount=createHook("bum"),onUnmounted=createHook("um"),onServerPrefetch=createHook("sp"),onRenderTriggered=createHook("rtg"),onRenderTracked=createHook("rtc");function onErrorCaptured(e,t=currentInstance){injectHook("ec",e,t)}const COMPONENTS="components",DIRECTIVES="directives";function resolveComponent(e,t){return resolveAsset(COMPONENTS,e,!0,t)||e}const NULL_DYNAMIC_COMPONENT=Symbol.for("v-ndc");function resolveDynamicComponent(e){return isString(e)?resolveAsset(COMPONENTS,e,!1)||e:e||NULL_DYNAMIC_COMPONENT}function resolveDirective(e){return resolveAsset(DIRECTIVES,e)}function resolveAsset(e,t,n=!0,s=!1){const r=currentRenderingInstance||currentInstance;if(r){const n=r.type;if(e===COMPONENTS){const e=getComponentName(n,!1);if(e&&(e===t||e===camelize(t)||e===capitalize(camelize(t))))return n}const o=resolve(r[e]||n[e],t)||resolve(r.appContext[e],t);return!o&&s?n:o}}function resolve(e,t){return e&&(e[t]||e[camelize(t)]||e[capitalize(camelize(t))])}function renderList(e,t,n,s){let r;const o=n&&n[s],i=isArray(e);if(i||isString(e)){let n=!1,s=!1;i&&isReactive(e)&&(n=!isShallow(e),s=isReadonly(e),e=shallowReadArray(e)),r=new Array(e.length);for(let i=0,a=e.length;i<a;i++)r[i]=t(n?s?toReadonly(toReactive(e[i])):toReactive(e[i]):e[i],i,void 0,o&&o[i])}else if("number"==typeof e){r=new Array(e);for(let n=0;n<e;n++)r[n]=t(n+1,n,void 0,o&&o[n])}else if(isObject(e))if(e[Symbol.iterator])r=Array.from(e,(e,n)=>t(e,n,void 0,o&&o[n]));else{const n=Object.keys(e);r=new Array(n.length);for(let s=0,i=n.length;s<i;s++){const i=n[s];r[s]=t(e[i],i,s,o&&o[s])}}else r=[];return n&&(n[s]=r),r}function createSlots(e,t){for(let n=0;n<t.length;n++){const s=t[n];if(isArray(s))for(let t=0;t<s.length;t++)e[s[t].name]=s[t].fn;else s&&(e[s.name]=s.key?(...e)=>{const t=s.fn(...e);return t&&(t.key=s.key),t}:s.fn)}return e}function renderSlot(e,t,n={},s,r){if(currentRenderingInstance.ce||currentRenderingInstance.parent&&isAsyncWrapper(currentRenderingInstance.parent)&&currentRenderingInstance.parent.ce)return"default"!==t&&(n.name=t),openBlock(),createBlock(Fragment,null,[createVNode("slot",n,s&&s())],64);let o=e[t];o&&o._c&&(o._d=!1),openBlock();const i=o&&ensureValidVNode(o(n)),a=n.key||i&&i.key,c=createBlock(Fragment,{key:(a&&!isSymbol(a)?a:`_${t}`)+(!i&&s?"_fb":"")},i||(s?s():[]),i&&1===e._?64:-2);return!r&&c.scopeId&&(c.slotScopeIds=[c.scopeId+"-s"]),o&&o._c&&(o._d=!0),c}function ensureValidVNode(e){return e.some(e=>!isVNode(e)||e.type!==Comment&&!(e.type===Fragment&&!ensureValidVNode(e.children)))?e:null}function toHandlers(e,t){const n={};for(const s in e)n[t&&/[A-Z]/.test(s)?`on:${s}`:toHandlerKey(s)]=e[s];return n}const getPublicInstance=e=>e?isStatefulComponent(e)?getComponentPublicInstance(e):getPublicInstance(e.parent):null,publicPropertiesMap=extend(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>getPublicInstance(e.parent),$root:e=>getPublicInstance(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>resolveMergedOptions(e),$forceUpdate:e=>e.f||(e.f=()=>{queueJob(e.update)}),$nextTick:e=>e.n||(e.n=nextTick.bind(e.proxy)),$watch:e=>instanceWatch.bind(e)}),hasSetupBinding=(e,t)=>e!==EMPTY_OBJ&&!e.__isScriptSetup&&hasOwn(e,t),PublicInstanceProxyHandlers={get({_:e},t){if("__v_skip"===t)return!0;const{ctx:n,setupState:s,data:r,props:o,accessCache:i,type:a,appContext:c}=e;let l;if("$"!==t[0]){const a=i[t];if(void 0!==a)switch(a){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return o[t]}else{if(hasSetupBinding(s,t))return i[t]=1,s[t];if(r!==EMPTY_OBJ&&hasOwn(r,t))return i[t]=2,r[t];if((l=e.propsOptions[0])&&hasOwn(l,t))return i[t]=3,o[t];if(n!==EMPTY_OBJ&&hasOwn(n,t))return i[t]=4,n[t];shouldCacheAccess&&(i[t]=0)}}const d=publicPropertiesMap[t];let u,p;return d?("$attrs"===t&&track(e.attrs,"get",""),d(e)):(u=a.__cssModules)&&(u=u[t])?u:n!==EMPTY_OBJ&&hasOwn(n,t)?(i[t]=4,n[t]):(p=c.config.globalProperties,hasOwn(p,t)?p[t]:void 0)},set({_:e},t,n){const{data:s,setupState:r,ctx:o}=e;return hasSetupBinding(r,t)?(r[t]=n,!0):s!==EMPTY_OBJ&&hasOwn(s,t)?(s[t]=n,!0):!hasOwn(e.props,t)&&(("$"!==t[0]||!(t.slice(1)in e))&&(o[t]=n,!0))},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:o}},i){let a;return!!n[i]||e!==EMPTY_OBJ&&hasOwn(e,i)||hasSetupBinding(t,i)||(a=o[0])&&hasOwn(a,i)||hasOwn(s,i)||hasOwn(publicPropertiesMap,i)||hasOwn(r.config.globalProperties,i)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:hasOwn(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}},RuntimeCompiledPublicInstanceProxyHandlers=extend({},PublicInstanceProxyHandlers,{get(e,t){if(t!==Symbol.unscopables)return PublicInstanceProxyHandlers.get(e,t,e)},has:(e,t)=>"_"!==t[0]&&!isGloballyAllowed(t)});function defineProps(){return null}function defineEmits(){return null}function defineExpose(e){}function defineOptions(e){}function defineSlots(){return null}function defineModel(){}function withDefaults(e,t){return null}function useSlots(){return getContext().slots}function useAttrs(){return getContext().attrs}function getContext(e){const t=getCurrentInstance();return t.setupContext||(t.setupContext=createSetupContext(t))}function normalizePropsOrEmits(e){return isArray(e)?e.reduce((e,t)=>(e[t]=null,e),{}):e}function mergeDefaults(e,t){const n=normalizePropsOrEmits(e);for(const s in t){if(s.startsWith("__skip"))continue;let e=n[s];e?isArray(e)||isFunction(e)?e=n[s]={type:e,default:t[s]}:e.default=t[s]:null===e&&(e=n[s]={default:t[s]}),e&&t[`__skip_${s}`]&&(e.skipFactory=!0)}return n}function mergeModels(e,t){return e&&t?isArray(e)&&isArray(t)?e.concat(t):extend({},normalizePropsOrEmits(e),normalizePropsOrEmits(t)):e||t}function createPropsRestProxy(e,t){const n={};for(const s in e)t.includes(s)||Object.defineProperty(n,s,{enumerable:!0,get:()=>e[s]});return n}function withAsyncContext(e){const t=getCurrentInstance();let n=e();return unsetCurrentInstance(),isPromise(n)&&(n=n.catch(e=>{throw setCurrentInstance(t),e})),[n,()=>setCurrentInstance(t)]}let shouldCacheAccess=!0;function applyOptions(e){const t=resolveMergedOptions(e),n=e.proxy,s=e.ctx;shouldCacheAccess=!1,t.beforeCreate&&callHook$1(t.beforeCreate,e,"bc");const{data:r,computed:o,methods:i,watch:a,provide:c,inject:l,created:d,beforeMount:u,mounted:p,beforeUpdate:h,updated:f,activated:m,deactivated:g,beforeDestroy:y,beforeUnmount:v,destroyed:S,unmounted:_,render:b,renderTracked:E,renderTriggered:C,errorCaptured:T,serverPrefetch:N,expose:A,inheritAttrs:R,components:x,directives:O,filters:k}=t;if(l&&resolveInjections(l,s,null),i)for(const w in i){const e=i[w];isFunction(e)&&(s[w]=e.bind(n))}if(r){const t=r.call(n,n);isObject(t)&&(e.data=reactive(t))}if(shouldCacheAccess=!0,o)for(const w in o){const e=o[w],t=isFunction(e)?e.bind(n,n):isFunction(e.get)?e.get.bind(n,n):NOOP,r=!isFunction(e)&&isFunction(e.set)?e.set.bind(n):NOOP,i=computed({get:t,set:r});Object.defineProperty(s,w,{enumerable:!0,configurable:!0,get:()=>i.value,set:e=>i.value=e})}if(a)for(const w in a)createWatcher(a[w],s,n,w);if(c){const e=isFunction(c)?c.call(n):c;Reflect.ownKeys(e).forEach(t=>{provide(t,e[t])})}function I(e,t){isArray(t)?t.forEach(t=>e(t.bind(n))):t&&e(t.bind(n))}if(d&&callHook$1(d,e,"c"),I(onBeforeMount,u),I(onMounted,p),I(onBeforeUpdate,h),I(onUpdated,f),I(onActivated,m),I(onDeactivated,g),I(onErrorCaptured,T),I(onRenderTracked,E),I(onRenderTriggered,C),I(onBeforeUnmount,v),I(onUnmounted,_),I(onServerPrefetch,N),isArray(A))if(A.length){const t=e.exposed||(e.exposed={});A.forEach(e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t,enumerable:!0})})}else e.exposed||(e.exposed={});b&&e.render===NOOP&&(e.render=b),null!=R&&(e.inheritAttrs=R),x&&(e.components=x),O&&(e.directives=O),N&&markAsyncBoundary(e)}function resolveInjections(e,t,n=NOOP){isArray(e)&&(e=normalizeInject(e));for(const s in e){const n=e[s];let r;r=isObject(n)?"default"in n?inject(n.from||s,n.default,!0):inject(n.from||s):inject(n),isRef(r)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>r.value,set:e=>r.value=e}):t[s]=r}}function callHook$1(e,t,n){callWithAsyncErrorHandling(isArray(e)?e.map(e=>e.bind(t.proxy)):e.bind(t.proxy),t,n)}function createWatcher(e,t,n,s){let r=s.includes(".")?createPathGetter(n,s):()=>n[s];if(isString(e)){const n=t[e];isFunction(n)&&watch(r,n)}else if(isFunction(e))watch(r,e.bind(n));else if(isObject(e))if(isArray(e))e.forEach(e=>createWatcher(e,t,n,s));else{const s=isFunction(e.handler)?e.handler.bind(n):t[e.handler];isFunction(s)&&watch(r,s,e)}}function resolveMergedOptions(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:r,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,a=o.get(t);let c;return a?c=a:r.length||n||s?(c={},r.length&&r.forEach(e=>mergeOptions(c,e,i,!0)),mergeOptions(c,t,i)):c=t,isObject(t)&&o.set(t,c),c}function mergeOptions(e,t,n,s=!1){const{mixins:r,extends:o}=t;o&&mergeOptions(e,o,n,!0),r&&r.forEach(t=>mergeOptions(e,t,n,!0));for(const i in t)if(s&&"expose"===i);else{const s=internalOptionMergeStrats[i]||n&&n[i];e[i]=s?s(e[i],t[i]):t[i]}return e}const internalOptionMergeStrats={data:mergeDataFn,props:mergeEmitsOrPropsOptions,emits:mergeEmitsOrPropsOptions,methods:mergeObjectOptions,computed:mergeObjectOptions,beforeCreate:mergeAsArray$1,created:mergeAsArray$1,beforeMount:mergeAsArray$1,mounted:mergeAsArray$1,beforeUpdate:mergeAsArray$1,updated:mergeAsArray$1,beforeDestroy:mergeAsArray$1,beforeUnmount:mergeAsArray$1,destroyed:mergeAsArray$1,unmounted:mergeAsArray$1,activated:mergeAsArray$1,deactivated:mergeAsArray$1,errorCaptured:mergeAsArray$1,serverPrefetch:mergeAsArray$1,components:mergeObjectOptions,directives:mergeObjectOptions,watch:mergeWatchOptions,provide:mergeDataFn,inject:mergeInject};function mergeDataFn(e,t){return t?e?function(){return extend(isFunction(e)?e.call(this,this):e,isFunction(t)?t.call(this,this):t)}:t:e}function mergeInject(e,t){return mergeObjectOptions(normalizeInject(e),normalizeInject(t))}function normalizeInject(e){if(isArray(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function mergeAsArray$1(e,t){return e?[...new Set([].concat(e,t))]:t}function mergeObjectOptions(e,t){return e?extend(Object.create(null),e,t):t}function mergeEmitsOrPropsOptions(e,t){return e?isArray(e)&&isArray(t)?[...new Set([...e,...t])]:extend(Object.create(null),normalizePropsOrEmits(e),normalizePropsOrEmits(null!=t?t:{})):t}function mergeWatchOptions(e,t){if(!e)return t;if(!t)return e;const n=extend(Object.create(null),e);for(const s in t)n[s]=mergeAsArray$1(e[s],t[s]);return n}function createAppContext(){return{app:null,config:{isNativeTag:NO,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let uid$1=0;function createAppAPI(e,t){return function(n,s=null){isFunction(n)||(n=extend({},n)),null==s||isObject(s)||(s=null);const r=createAppContext(),o=new WeakSet,i=[];let a=!1;const c=r.app={_uid:uid$1++,_component:n,_props:s,_container:null,_context:r,_instance:null,version:version,get config(){return r.config},set config(e){},use:(e,...t)=>(o.has(e)||(e&&isFunction(e.install)?(o.add(e),e.install(c,...t)):isFunction(e)&&(o.add(e),e(c,...t))),c),mixin:e=>(r.mixins.includes(e)||r.mixins.push(e),c),component:(e,t)=>t?(r.components[e]=t,c):r.components[e],directive:(e,t)=>t?(r.directives[e]=t,c):r.directives[e],mount(o,i,l){if(!a){const d=c._ceVNode||createVNode(n,s);return d.appContext=r,!0===l?l="svg":!1===l&&(l=void 0),i&&t?t(d,o):e(d,o,l),a=!0,c._container=o,o.__vue_app__=c,getComponentPublicInstance(d.component)}},onUnmount(e){i.push(e)},unmount(){a&&(callWithAsyncErrorHandling(i,c._instance,16),e(null,c._container),delete c._container.__vue_app__)},provide:(e,t)=>(r.provides[e]=t,c),runWithContext(e){const t=currentApp;currentApp=c;try{return e()}finally{currentApp=t}}};return c}}let currentApp=null;function provide(e,t){if(currentInstance){let n=currentInstance.provides;const s=currentInstance.parent&&currentInstance.parent.provides;s===n&&(n=currentInstance.provides=Object.create(s)),n[e]=t}else;}function inject(e,t,n=!1){const s=getCurrentInstance();if(s||currentApp){let r=currentApp?currentApp._context.provides:s?null==s.parent||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&isFunction(t)?t.call(s&&s.proxy):t}}function hasInjectionContext(){return!(!getCurrentInstance()&&!currentApp)}const internalObjectProto={},createInternalObject=()=>Object.create(internalObjectProto),isInternalObject=e=>Object.getPrototypeOf(e)===internalObjectProto;function initProps(e,t,n,s=!1){const r={},o=createInternalObject();e.propsDefaults=Object.create(null),setFullProps(e,t,r,o);for(const i in e.propsOptions[0])i in r||(r[i]=void 0);n?e.props=s?r:shallowReactive(r):e.type.props?e.props=r:e.props=o,e.attrs=o}function updateProps(e,t,n,s){const{props:r,attrs:o,vnode:{patchFlag:i}}=e,a=toRaw(r),[c]=e.propsOptions;let l=!1;if(!(s||i>0)||16&i){let s;setFullProps(e,t,r,o)&&(l=!0);for(const o in a)t&&(hasOwn(t,o)||(s=hyphenate(o))!==o&&hasOwn(t,s))||(c?!n||void 0===n[o]&&void 0===n[s]||(r[o]=resolvePropValue(c,a,o,void 0,e,!0)):delete r[o]);if(o!==a)for(const e in o)t&&hasOwn(t,e)||(delete o[e],l=!0)}else if(8&i){const n=e.vnode.dynamicProps;for(let s=0;s<n.length;s++){let i=n[s];if(isEmitListener(e.emitsOptions,i))continue;const d=t[i];if(c)if(hasOwn(o,i))d!==o[i]&&(o[i]=d,l=!0);else{const t=camelize(i);r[t]=resolvePropValue(c,a,t,d,e,!1)}else d!==o[i]&&(o[i]=d,l=!0)}}l&&trigger(e.attrs,"set","")}function setFullProps(e,t,n,s){const[r,o]=e.propsOptions;let i,a=!1;if(t)for(let c in t){if(isReservedProp(c))continue;const l=t[c];let d;r&&hasOwn(r,d=camelize(c))?o&&o.includes(d)?(i||(i={}))[d]=l:n[d]=l:isEmitListener(e.emitsOptions,c)||c in s&&l===s[c]||(s[c]=l,a=!0)}if(o){const t=toRaw(n),s=i||EMPTY_OBJ;for(let i=0;i<o.length;i++){const a=o[i];n[a]=resolvePropValue(r,t,a,s[a],e,!hasOwn(s,a))}}return a}function resolvePropValue(e,t,n,s,r,o){const i=e[n];if(null!=i){const e=hasOwn(i,"default");if(e&&void 0===s){const e=i.default;if(i.type!==Function&&!i.skipFactory&&isFunction(e)){const{propsDefaults:o}=r;if(n in o)s=o[n];else{const i=setCurrentInstance(r);s=o[n]=e.call(null,t),i()}}else s=e;r.ce&&r.ce._setProp(n,s)}i[0]&&(o&&!e?s=!1:!i[1]||""!==s&&s!==hyphenate(n)||(s=!0))}return s}const mixinPropsCache=new WeakMap;function normalizePropsOptions(e,t,n=!1){const s=n?mixinPropsCache:t.propsCache,r=s.get(e);if(r)return r;const o=e.props,i={},a=[];let c=!1;if(!isFunction(e)){const s=e=>{c=!0;const[n,s]=normalizePropsOptions(e,t,!0);extend(i,n),s&&a.push(...s)};!n&&t.mixins.length&&t.mixins.forEach(s),e.extends&&s(e.extends),e.mixins&&e.mixins.forEach(s)}if(!o&&!c)return isObject(e)&&s.set(e,EMPTY_ARR),EMPTY_ARR;if(isArray(o))for(let d=0;d<o.length;d++){const e=camelize(o[d]);validatePropName(e)&&(i[e]=EMPTY_OBJ)}else if(o)for(const d in o){const e=camelize(d);if(validatePropName(e)){const t=o[d],n=i[e]=isArray(t)||isFunction(t)?{type:t}:extend({},t),s=n.type;let r=!1,c=!0;if(isArray(s))for(let e=0;e<s.length;++e){const t=s[e],n=isFunction(t)&&t.name;if("Boolean"===n){r=!0;break}"String"===n&&(c=!1)}else r=isFunction(s)&&"Boolean"===s.name;n[0]=r,n[1]=c,(r||hasOwn(n,"default"))&&a.push(e)}}const l=[i,a];return isObject(e)&&s.set(e,l),l}function validatePropName(e){return"$"!==e[0]&&!isReservedProp(e)}const isInternalKey=e=>"_"===e||"__"===e||"_ctx"===e||"$stable"===e,normalizeSlotValue=e=>isArray(e)?e.map(normalizeVNode):[normalizeVNode(e)],normalizeSlot=(e,t,n)=>{if(t._n)return t;const s=withCtx((...e)=>normalizeSlotValue(t(...e)),n);return s._c=!1,s},normalizeObjectSlots=(e,t,n)=>{const s=e._ctx;for(const r in e){if(isInternalKey(r))continue;const n=e[r];if(isFunction(n))t[r]=normalizeSlot(r,n,s);else if(null!=n){const e=normalizeSlotValue(n);t[r]=()=>e}}},normalizeVNodeSlots=(e,t)=>{const n=normalizeSlotValue(t);e.slots.default=()=>n},assignSlots=(e,t,n)=>{for(const s in t)!n&&isInternalKey(s)||(e[s]=t[s])},initSlots=(e,t,n)=>{const s=e.slots=createInternalObject();if(32&e.vnode.shapeFlag){const e=t.__;e&&def(s,"__",e,!0);const r=t._;r?(assignSlots(s,t,n),n&&def(s,"_",r,!0)):normalizeObjectSlots(t,s)}else t&&normalizeVNodeSlots(e,t)},updateSlots=(e,t,n)=>{const{vnode:s,slots:r}=e;let o=!0,i=EMPTY_OBJ;if(32&s.shapeFlag){const e=t._;e?n&&1===e?o=!1:assignSlots(r,t,n):(o=!t.$stable,normalizeObjectSlots(t,r)),i=t}else t&&(normalizeVNodeSlots(e,t),i={default:1});if(o)for(const a in r)isInternalKey(a)||null!=i[a]||delete r[a]},queuePostRenderEffect=queueEffectWithSuspense;function createRenderer(e){return baseCreateRenderer(e)}function createHydrationRenderer(e){return baseCreateRenderer(e,createHydrationFunctions)}function baseCreateRenderer(e,t){getGlobalThis().__VUE__=!0;const{insert:n,remove:s,patchProp:r,createElement:o,createText:i,createComment:a,setText:c,setElementText:l,parentNode:d,nextSibling:u,setScopeId:p=NOOP,insertStaticContent:h}=e,f=(e,t,n,s=null,r=null,o=null,i=void 0,a=null,c=!!t.dynamicChildren)=>{if(e===t)return;e&&!isSameVNodeType(e,t)&&(s=H(e),M(e,r,o,!0),e=null),-2===t.patchFlag&&(c=!1,t.dynamicChildren=null);const{type:l,ref:d,shapeFlag:u}=t;switch(l){case Text:m(e,t,n,s);break;case Comment:g(e,t,n,s);break;case Static:null==e&&y(t,n,s,i);break;case Fragment:N(e,t,n,s,r,o,i,a,c);break;default:1&u?v(e,t,n,s,r,o,i,a,c):6&u?A(e,t,n,s,r,o,i,a,c):(64&u||128&u)&&l.process(e,t,n,s,r,o,i,a,c,K)}null!=d&&r?setRef(d,e&&e.ref,o,t||e,!t):null==d&&e&&null!=e.ref&&setRef(e.ref,null,o,e,!0)},m=(e,t,s,r)=>{if(null==e)n(t.el=i(t.children),s,r);else{const n=t.el=e.el;t.children!==e.children&&c(n,t.children)}},g=(e,t,s,r)=>{null==e?n(t.el=a(t.children||""),s,r):t.el=e.el},y=(e,t,n,s)=>{[e.el,e.anchor]=h(e.children,t,n,s,e.el,e.anchor)},v=(e,t,n,s,r,o,i,a,c)=>{"svg"===t.type?i="svg":"math"===t.type&&(i="mathml"),null==e?S(t,n,s,r,o,i,a,c):E(e,t,r,o,i,a,c)},S=(e,t,s,i,a,c,d,u)=>{let p,h;const{props:f,shapeFlag:m,transition:g,dirs:y}=e;if(p=e.el=o(e.type,c,f&&f.is,f),8&m?l(p,e.children):16&m&&b(e.children,p,null,i,a,resolveChildrenNamespace(e,c),d,u),y&&invokeDirectiveHook(e,null,i,"created"),_(p,e,e.scopeId,d,i),f){for(const e in f)"value"===e||isReservedProp(e)||r(p,e,null,f[e],c,i);"value"in f&&r(p,"value",null,f.value,c),(h=f.onVnodeBeforeMount)&&invokeVNodeHook(h,i,e)}y&&invokeDirectiveHook(e,null,i,"beforeMount");const v=needTransition(a,g);v&&g.beforeEnter(p),n(p,t,s),((h=f&&f.onVnodeMounted)||v||y)&&queuePostRenderEffect(()=>{h&&invokeVNodeHook(h,i,e),v&&g.enter(p),y&&invokeDirectiveHook(e,null,i,"mounted")},a)},_=(e,t,n,s,r)=>{if(n&&p(e,n),s)for(let o=0;o<s.length;o++)p(e,s[o]);if(r){let n=r.subTree;if(t===n||isSuspense(n.type)&&(n.ssContent===t||n.ssFallback===t)){const t=r.vnode;_(e,t,t.scopeId,t.slotScopeIds,r.parent)}}},b=(e,t,n,s,r,o,i,a,c=0)=>{for(let l=c;l<e.length;l++){const c=e[l]=a?cloneIfMounted(e[l]):normalizeVNode(e[l]);f(null,c,t,n,s,r,o,i,a)}},E=(e,t,n,s,o,i,a)=>{const c=t.el=e.el;let{patchFlag:d,dynamicChildren:u,dirs:p}=t;d|=16&e.patchFlag;const h=e.props||EMPTY_OBJ,f=t.props||EMPTY_OBJ;let m;if(n&&toggleRecurse(n,!1),(m=f.onVnodeBeforeUpdate)&&invokeVNodeHook(m,n,t,e),p&&invokeDirectiveHook(t,e,n,"beforeUpdate"),n&&toggleRecurse(n,!0),(h.innerHTML&&null==f.innerHTML||h.textContent&&null==f.textContent)&&l(c,""),u?C(e.dynamicChildren,u,c,n,s,resolveChildrenNamespace(t,o),i):a||I(e,t,c,null,n,s,resolveChildrenNamespace(t,o),i,!1),d>0){if(16&d)T(c,h,f,n,o);else if(2&d&&h.class!==f.class&&r(c,"class",null,f.class,o),4&d&&r(c,"style",h.style,f.style,o),8&d){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const s=e[t],i=h[s],a=f[s];a===i&&"value"!==s||r(c,s,i,a,o,n)}}1&d&&e.children!==t.children&&l(c,t.children)}else a||null!=u||T(c,h,f,n,o);((m=f.onVnodeUpdated)||p)&&queuePostRenderEffect(()=>{m&&invokeVNodeHook(m,n,t,e),p&&invokeDirectiveHook(t,e,n,"updated")},s)},C=(e,t,n,s,r,o,i)=>{for(let a=0;a<t.length;a++){const c=e[a],l=t[a],u=c.el&&(c.type===Fragment||!isSameVNodeType(c,l)||198&c.shapeFlag)?d(c.el):n;f(c,l,u,null,s,r,o,i,!0)}},T=(e,t,n,s,o)=>{if(t!==n){if(t!==EMPTY_OBJ)for(const i in t)isReservedProp(i)||i in n||r(e,i,t[i],null,o,s);for(const i in n){if(isReservedProp(i))continue;const a=n[i],c=t[i];a!==c&&"value"!==i&&r(e,i,c,a,o,s)}"value"in n&&r(e,"value",t.value,n.value,o)}},N=(e,t,s,r,o,a,c,l,d)=>{const u=t.el=e?e.el:i(""),p=t.anchor=e?e.anchor:i("");let{patchFlag:h,dynamicChildren:f,slotScopeIds:m}=t;m&&(l=l?l.concat(m):m),null==e?(n(u,s,r),n(p,s,r),b(t.children||[],s,p,o,a,c,l,d)):h>0&&64&h&&f&&e.dynamicChildren?(C(e.dynamicChildren,f,s,o,a,c,l),(null!=t.key||o&&t===o.subTree)&&traverseStaticChildren(e,t,!0)):I(e,t,s,p,o,a,c,l,d)},A=(e,t,n,s,r,o,i,a,c)=>{t.slotScopeIds=a,null==e?512&t.shapeFlag?r.ctx.activate(t,n,s,i,c):R(t,n,s,r,o,i,c):x(e,t,c)},R=(e,t,n,s,r,o,i)=>{const a=e.component=createComponentInstance(e,s,r);if(isKeepAlive(e)&&(a.ctx.renderer=K),setupComponent(a,!1,i),a.asyncDep){if(r&&r.registerDep(a,O,i),!e.el){const s=a.subTree=createVNode(Comment);g(null,s,t,n),e.placeholder=s.el}}else O(a,e,t,n,r,o,i)},x=(e,t,n)=>{const s=t.component=e.component;if(shouldUpdateComponent(e,t,n)){if(s.asyncDep&&!s.asyncResolved)return void k(s,t,n);s.next=t,s.update()}else t.el=e.el,s.vnode=t},O=(e,t,n,s,r,o,i)=>{const a=()=>{if(e.isMounted){let{next:t,bu:n,u:s,parent:c,vnode:l}=e;{const n=locateNonHydratedAsyncRoot(e);if(n)return t&&(t.el=l.el,k(e,t,i)),void n.asyncDep.then(()=>{e.isUnmounted||a()})}let u,p=t;toggleRecurse(e,!1),t?(t.el=l.el,k(e,t,i)):t=l,n&&invokeArrayFns(n),(u=t.props&&t.props.onVnodeBeforeUpdate)&&invokeVNodeHook(u,c,t,l),toggleRecurse(e,!0);const h=renderComponentRoot(e),m=e.subTree;e.subTree=h,f(m,h,d(m.el),H(m),e,r,o),t.el=h.el,null===p&&updateHOCHostEl(e,h.el),s&&queuePostRenderEffect(s,r),(u=t.props&&t.props.onVnodeUpdated)&&queuePostRenderEffect(()=>invokeVNodeHook(u,c,t,l),r)}else{let i;const{el:a,props:c}=t,{bm:l,m:d,parent:u,root:p,type:h}=e,m=isAsyncWrapper(t);if(toggleRecurse(e,!1),l&&invokeArrayFns(l),!m&&(i=c&&c.onVnodeBeforeMount)&&invokeVNodeHook(i,u,t),toggleRecurse(e,!0),a&&z){const t=()=>{e.subTree=renderComponentRoot(e),z(a,e.subTree,e,r,null)};m&&h.__asyncHydrate?h.__asyncHydrate(a,e,t):t()}else{p.ce&&!1!==p.ce._def.shadowRoot&&p.ce._injectChildStyle(h);const i=e.subTree=renderComponentRoot(e);f(null,i,n,s,e,r,o),t.el=i.el}if(d&&queuePostRenderEffect(d,r),!m&&(i=c&&c.onVnodeMounted)){const e=t;queuePostRenderEffect(()=>invokeVNodeHook(i,u,e),r)}(256&t.shapeFlag||u&&isAsyncWrapper(u.vnode)&&256&u.vnode.shapeFlag)&&e.a&&queuePostRenderEffect(e.a,r),e.isMounted=!0,t=n=s=null}};e.scope.on();const c=e.effect=new ReactiveEffect(a);e.scope.off();const l=e.update=c.run.bind(c),u=e.job=c.runIfDirty.bind(c);u.i=e,u.id=e.uid,c.scheduler=()=>queueJob(u),toggleRecurse(e,!0),l()},k=(e,t,n)=>{t.component=e;const s=e.vnode.props;e.vnode=t,e.next=null,updateProps(e,t.props,s,n),updateSlots(e,t.children,n),pauseTracking(),flushPreFlushCbs(e),resetTracking()},I=(e,t,n,s,r,o,i,a,c=!1)=>{const d=e&&e.children,u=e?e.shapeFlag:0,p=t.children,{patchFlag:h,shapeFlag:f}=t;if(h>0){if(128&h)return void P(d,p,n,s,r,o,i,a,c);if(256&h)return void w(d,p,n,s,r,o,i,a,c)}8&f?(16&u&&F(d,r,o),p!==d&&l(n,p)):16&u?16&f?P(d,p,n,s,r,o,i,a,c):F(d,r,o,!0):(8&u&&l(n,""),16&f&&b(p,n,s,r,o,i,a,c))},w=(e,t,n,s,r,o,i,a,c)=>{t=t||EMPTY_ARR;const l=(e=e||EMPTY_ARR).length,d=t.length,u=Math.min(l,d);let p;for(p=0;p<u;p++){const s=t[p]=c?cloneIfMounted(t[p]):normalizeVNode(t[p]);f(e[p],s,n,null,r,o,i,a,c)}l>d?F(e,r,o,!0,!1,u):b(t,n,s,r,o,i,a,c,u)},P=(e,t,n,s,r,o,i,a,c)=>{let l=0;const d=t.length;let u=e.length-1,p=d-1;for(;l<=u&&l<=p;){const s=e[l],d=t[l]=c?cloneIfMounted(t[l]):normalizeVNode(t[l]);if(!isSameVNodeType(s,d))break;f(s,d,n,null,r,o,i,a,c),l++}for(;l<=u&&l<=p;){const s=e[u],l=t[p]=c?cloneIfMounted(t[p]):normalizeVNode(t[p]);if(!isSameVNodeType(s,l))break;f(s,l,n,null,r,o,i,a,c),u--,p--}if(l>u){if(l<=p){const e=p+1,u=e<d?t[e].el:s;for(;l<=p;)f(null,t[l]=c?cloneIfMounted(t[l]):normalizeVNode(t[l]),n,u,r,o,i,a,c),l++}}else if(l>p)for(;l<=u;)M(e[l],r,o,!0),l++;else{const h=l,m=l,g=new Map;for(l=m;l<=p;l++){const e=t[l]=c?cloneIfMounted(t[l]):normalizeVNode(t[l]);null!=e.key&&g.set(e.key,l)}let y,v=0;const S=p-m+1;let _=!1,b=0;const E=new Array(S);for(l=0;l<S;l++)E[l]=0;for(l=h;l<=u;l++){const s=e[l];if(v>=S){M(s,r,o,!0);continue}let d;if(null!=s.key)d=g.get(s.key);else for(y=m;y<=p;y++)if(0===E[y-m]&&isSameVNodeType(s,t[y])){d=y;break}void 0===d?M(s,r,o,!0):(E[d-m]=l+1,d>=b?b=d:_=!0,f(s,t[d],n,null,r,o,i,a,c),v++)}const C=_?getSequence(E):EMPTY_ARR;for(y=C.length-1,l=S-1;l>=0;l--){const e=m+l,u=t[e],p=t[e+1],h=e+1<d?p.el||p.placeholder:s;0===E[l]?f(null,u,n,h,r,o,i,a,c):_&&(y<0||l!==C[y]?V(u,n,h,2):y--)}}},V=(e,t,r,o,i=null)=>{const{el:a,type:c,transition:l,children:d,shapeFlag:p}=e;if(6&p)return void V(e.component.subTree,t,r,o);if(128&p)return void e.suspense.move(t,r,o);if(64&p)return void c.move(e,t,r,K);if(c===Fragment){n(a,t,r);for(let e=0;e<d.length;e++)V(d[e],t,r,o);return void n(e.anchor,t,r)}if(c===Static)return void(({el:e,anchor:t},s,r)=>{let o;for(;e&&e!==t;)o=u(e),n(e,s,r),e=o;n(t,s,r)})(e,t,r);if(2!==o&&1&p&&l)if(0===o)l.beforeEnter(a),n(a,t,r),queuePostRenderEffect(()=>l.enter(a),i);else{const{leave:o,delayLeave:i,afterLeave:c}=l,d=()=>{e.ctx.isUnmounted?s(a):n(a,t,r)},u=()=>{o(a,()=>{d(),c&&c()})};i?i(a,d,u):u()}else n(a,t,r)},M=(e,t,n,s=!1,r=!1)=>{const{type:o,props:i,ref:a,children:c,dynamicChildren:l,shapeFlag:d,patchFlag:u,dirs:p,cacheIndex:h}=e;if(-2===u&&(r=!1),null!=a&&(pauseTracking(),setRef(a,null,n,e,!0),resetTracking()),null!=h&&(t.renderCache[h]=void 0),256&d)return void t.ctx.deactivate(e);const f=1&d&&p,m=!isAsyncWrapper(e);let g;if(m&&(g=i&&i.onVnodeBeforeUnmount)&&invokeVNodeHook(g,t,e),6&d)L(e.component,n,s);else{if(128&d)return void e.suspense.unmount(n,s);f&&invokeDirectiveHook(e,null,t,"beforeUnmount"),64&d?e.type.remove(e,t,n,K,s):l&&!l.hasOnce&&(o!==Fragment||u>0&&64&u)?F(l,t,n,!1,!0):(o===Fragment&&384&u||!r&&16&d)&&F(c,t,n),s&&B(e)}(m&&(g=i&&i.onVnodeUnmounted)||f)&&queuePostRenderEffect(()=>{g&&invokeVNodeHook(g,t,e),f&&invokeDirectiveHook(e,null,t,"unmounted")},n)},B=e=>{const{type:t,el:n,anchor:r,transition:o}=e;if(t===Fragment)return void D(n,r);if(t===Static)return void(({el:e,anchor:t})=>{let n;for(;e&&e!==t;)n=u(e),s(e),e=n;s(t)})(e);const i=()=>{s(n),o&&!o.persisted&&o.afterLeave&&o.afterLeave()};if(1&e.shapeFlag&&o&&!o.persisted){const{leave:t,delayLeave:s}=o,r=()=>t(n,i);s?s(e.el,i,r):r()}else i()},D=(e,t)=>{let n;for(;e!==t;)n=u(e),s(e),e=n;s(t)},L=(e,t,n)=>{const{bum:s,scope:r,job:o,subTree:i,um:a,m:c,a:l,parent:d,slots:{__:u}}=e;invalidateMount(c),invalidateMount(l),s&&invokeArrayFns(s),d&&isArray(u)&&u.forEach(e=>{d.renderCache[e]=void 0}),r.stop(),o&&(o.flags|=8,M(i,e,t,n)),a&&queuePostRenderEffect(a,t),queuePostRenderEffect(()=>{e.isUnmounted=!0},t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},F=(e,t,n,s=!1,r=!1,o=0)=>{for(let i=o;i<e.length;i++)M(e[i],t,n,s,r)},H=e=>{if(6&e.shapeFlag)return H(e.component.subTree);if(128&e.shapeFlag)return e.suspense.next();const t=u(e.anchor||e.el),n=t&&t[TeleportEndKey];return n?u(n):t};let $=!1;const j=(e,t,n)=>{null==e?t._vnode&&M(t._vnode,null,null,!0):f(t._vnode||null,e,t,null,null,null,n),t._vnode=e,$||($=!0,flushPreFlushCbs(),flushPostFlushCbs(),$=!1)},K={p:f,um:M,m:V,r:B,mt:R,mc:b,pc:I,pbc:C,n:H,o:e};let U,z;return t&&([U,z]=t(K)),{render:j,hydrate:U,createApp:createAppAPI(j,U)}}function resolveChildrenNamespace({type:e,props:t},n){return"svg"===n&&"foreignObject"===e||"mathml"===n&&"annotation-xml"===e&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function toggleRecurse({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function needTransition(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function traverseStaticChildren(e,t,n=!1){const s=e.children,r=t.children;if(isArray(s)&&isArray(r))for(let o=0;o<s.length;o++){const e=s[o];let t=r[o];1&t.shapeFlag&&!t.dynamicChildren&&((t.patchFlag<=0||32===t.patchFlag)&&(t=r[o]=cloneIfMounted(r[o]),t.el=e.el),n||-2===t.patchFlag||traverseStaticChildren(e,t)),t.type===Text&&(t.el=e.el),t.type!==Comment||t.el||(t.el=e.el)}}function getSequence(e){const t=e.slice(),n=[0];let s,r,o,i,a;const c=e.length;for(s=0;s<c;s++){const c=e[s];if(0!==c){if(r=n[n.length-1],e[r]<c){t[s]=r,n.push(s);continue}for(o=0,i=n.length-1;o<i;)a=o+i>>1,e[n[a]]<c?o=a+1:i=a;c<e[n[o]]&&(o>0&&(t[s]=n[o-1]),n[o]=s)}}for(o=n.length,i=n[o-1];o-- >0;)n[o]=i,i=t[i];return n}function locateNonHydratedAsyncRoot(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:locateNonHydratedAsyncRoot(t)}function invalidateMount(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const ssrContextKey=Symbol.for("v-scx"),useSSRContext=()=>inject(ssrContextKey);function watchEffect(e,t){return doWatch(e,null,t)}function watchPostEffect(e,t){return doWatch(e,null,{flush:"post"})}function watchSyncEffect(e,t){return doWatch(e,null,{flush:"sync"})}function watch(e,t,n){return doWatch(e,t,n)}function doWatch(e,t,n=EMPTY_OBJ){const{immediate:s,deep:r,flush:o,once:i}=n,a=extend({},n),c=t&&s||!t&&"post"!==o;let l;if(isInSSRComponentSetup)if("sync"===o){const e=useSSRContext();l=e.__watcherHandles||(e.__watcherHandles=[])}else if(!c){const e=()=>{};return e.stop=NOOP,e.resume=NOOP,e.pause=NOOP,e}const d=currentInstance;a.call=(e,t,n)=>callWithAsyncErrorHandling(e,d,t,n);let u=!1;"post"===o?a.scheduler=e=>{queuePostRenderEffect(e,d&&d.suspense)}:"sync"!==o&&(u=!0,a.scheduler=(e,t)=>{t?e():queueJob(e)}),a.augmentJob=e=>{t&&(e.flags|=4),u&&(e.flags|=2,d&&(e.id=d.uid,e.i=d))};const p=watch$1(e,t,a);return isInSSRComponentSetup&&(l?l.push(p):c&&p()),p}function instanceWatch(e,t,n){const s=this.proxy,r=isString(e)?e.includes(".")?createPathGetter(s,e):()=>s[e]:e.bind(s,s);let o;isFunction(t)?o=t:(o=t.handler,n=t);const i=setCurrentInstance(this),a=doWatch(r,o.bind(s),n);return i(),a}function createPathGetter(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function useModel(e,t,n=EMPTY_OBJ){const s=getCurrentInstance(),r=camelize(t),o=hyphenate(t),i=getModelModifiers(e,r),a=customRef((i,a)=>{let c,l,d=EMPTY_OBJ;return watchSyncEffect(()=>{const t=e[r];hasChanged(c,t)&&(c=t,a())}),{get:()=>(i(),n.get?n.get(c):c),set(e){const i=n.set?n.set(e):e;if(!(hasChanged(i,c)||d!==EMPTY_OBJ&&hasChanged(e,d)))return;const u=s.vnode.props;u&&(t in u||r in u||o in u)&&(`onUpdate:${t}`in u||`onUpdate:${r}`in u||`onUpdate:${o}`in u)||(c=e,a()),s.emit(`update:${t}`,i),hasChanged(e,i)&&hasChanged(e,d)&&!hasChanged(i,l)&&a(),d=e,l=i}}});return a[Symbol.iterator]=()=>{let e=0;return{next:()=>e<2?{value:e++?i||EMPTY_OBJ:a,done:!1}:{done:!0}}},a}const getModelModifiers=(e,t)=>"modelValue"===t||"model-value"===t?e.modelModifiers:e[`${t}Modifiers`]||e[`${camelize(t)}Modifiers`]||e[`${hyphenate(t)}Modifiers`];function emit(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||EMPTY_OBJ;let r=n;const o=t.startsWith("update:"),i=o&&getModelModifiers(s,t.slice(7));let a;i&&(i.trim&&(r=n.map(e=>isString(e)?e.trim():e)),i.number&&(r=n.map(looseToNumber)));let c=s[a=toHandlerKey(t)]||s[a=toHandlerKey(camelize(t))];!c&&o&&(c=s[a=toHandlerKey(hyphenate(t))]),c&&callWithAsyncErrorHandling(c,e,6,r);const l=s[a+"Once"];if(l){if(e.emitted){if(e.emitted[a])return}else e.emitted={};e.emitted[a]=!0,callWithAsyncErrorHandling(l,e,6,r)}}function normalizeEmitsOptions(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(void 0!==r)return r;const o=e.emits;let i={},a=!1;if(!isFunction(e)){const s=e=>{const n=normalizeEmitsOptions(e,t,!0);n&&(a=!0,extend(i,n))};!n&&t.mixins.length&&t.mixins.forEach(s),e.extends&&s(e.extends),e.mixins&&e.mixins.forEach(s)}return o||a?(isArray(o)?o.forEach(e=>i[e]=null):extend(i,o),isObject(e)&&s.set(e,i),i):(isObject(e)&&s.set(e,null),null)}function isEmitListener(e,t){return!(!e||!isOn(t))&&(t=t.slice(2).replace(/Once$/,""),hasOwn(e,t[0].toLowerCase()+t.slice(1))||hasOwn(e,hyphenate(t))||hasOwn(e,t))}function markAttrsAccessed(){}function renderComponentRoot(e){const{type:t,vnode:n,proxy:s,withProxy:r,propsOptions:[o],slots:i,attrs:a,emit:c,render:l,renderCache:d,props:u,data:p,setupState:h,ctx:f,inheritAttrs:m}=e,g=setCurrentRenderingInstance(e);let y,v;try{if(4&n.shapeFlag){const e=r||s,t=e;y=normalizeVNode(l.call(t,e,d,u,h,p,f)),v=a}else{const e=t;0,y=normalizeVNode(e.length>1?e(u,{attrs:a,slots:i,emit:c}):e(u,null)),v=t.props?a:getFunctionalFallthrough(a)}}catch(_){blockStack.length=0,handleError(_,e,1),y=createVNode(Comment)}let S=y;if(v&&!1!==m){const e=Object.keys(v),{shapeFlag:t}=S;e.length&&7&t&&(o&&e.some(isModelListener)&&(v=filterModelListeners(v,o)),S=cloneVNode(S,v,!1,!0))}return n.dirs&&(S=cloneVNode(S,null,!1,!0),S.dirs=S.dirs?S.dirs.concat(n.dirs):n.dirs),n.transition&&setTransitionHooks(S,n.transition),y=S,setCurrentRenderingInstance(g),y}function filterSingleRoot(e,t=!0){let n;for(let s=0;s<e.length;s++){const t=e[s];if(!isVNode(t))return;if(t.type!==Comment||"v-if"===t.children){if(n)return;n=t}}return n}const getFunctionalFallthrough=e=>{let t;for(const n in e)("class"===n||"style"===n||isOn(n))&&((t||(t={}))[n]=e[n]);return t},filterModelListeners=(e,t)=>{const n={};for(const s in e)isModelListener(s)&&s.slice(9)in t||(n[s]=e[s]);return n};function shouldUpdateComponent(e,t,n){const{props:s,children:r,component:o}=e,{props:i,children:a,patchFlag:c}=t,l=o.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&c>=0))return!(!r&&!a||a&&a.$stable)||s!==i&&(s?!i||hasPropsChanged(s,i,l):!!i);if(1024&c)return!0;if(16&c)return s?hasPropsChanged(s,i,l):!!i;if(8&c){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t];if(i[n]!==s[n]&&!isEmitListener(l,n))return!0}}return!1}function hasPropsChanged(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const o=s[r];if(t[o]!==e[o]&&!isEmitListener(n,o))return!0}return!1}function updateHOCHostEl({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s!==e)break;(e=t.vnode).el=n,t=t.parent}}const isSuspense=e=>e.__isSuspense;let suspenseId=0;const SuspenseImpl={name:"Suspense",__isSuspense:!0,process(e,t,n,s,r,o,i,a,c,l){if(null==e)mountSuspense(t,n,s,r,o,i,a,c,l);else{if(o&&o.deps>0&&!e.suspense.isInFallback)return t.suspense=e.suspense,t.suspense.vnode=t,void(t.el=e.el);patchSuspense(e,t,n,s,r,i,a,c,l)}},hydrate:hydrateSuspense,normalize:normalizeSuspenseChildren},Suspense=SuspenseImpl;function triggerEvent(e,t){const n=e.props&&e.props[t];isFunction(n)&&n()}function mountSuspense(e,t,n,s,r,o,i,a,c){const{p:l,o:{createElement:d}}=c,u=d("div"),p=e.suspense=createSuspenseBoundary(e,r,s,t,u,n,o,i,a,c);l(null,p.pendingBranch=e.ssContent,u,null,s,p,o,i),p.deps>0?(triggerEvent(e,"onPending"),triggerEvent(e,"onFallback"),l(null,e.ssFallback,t,n,s,null,o,i),setActiveBranch(p,e.ssFallback)):p.resolve(!1,!0)}function patchSuspense(e,t,n,s,r,o,i,a,{p:c,um:l,o:{createElement:d}}){const u=t.suspense=e.suspense;u.vnode=t,t.el=e.el;const p=t.ssContent,h=t.ssFallback,{activeBranch:f,pendingBranch:m,isInFallback:g,isHydrating:y}=u;if(m)u.pendingBranch=p,isSameVNodeType(p,m)?(c(m,p,u.hiddenContainer,null,r,u,o,i,a),u.deps<=0?u.resolve():g&&(y||(c(f,h,n,s,r,null,o,i,a),setActiveBranch(u,h)))):(u.pendingId=suspenseId++,y?(u.isHydrating=!1,u.activeBranch=m):l(m,r,u),u.deps=0,u.effects.length=0,u.hiddenContainer=d("div"),g?(c(null,p,u.hiddenContainer,null,r,u,o,i,a),u.deps<=0?u.resolve():(c(f,h,n,s,r,null,o,i,a),setActiveBranch(u,h))):f&&isSameVNodeType(p,f)?(c(f,p,n,s,r,u,o,i,a),u.resolve(!0)):(c(null,p,u.hiddenContainer,null,r,u,o,i,a),u.deps<=0&&u.resolve()));else if(f&&isSameVNodeType(p,f))c(f,p,n,s,r,u,o,i,a),setActiveBranch(u,p);else if(triggerEvent(t,"onPending"),u.pendingBranch=p,512&p.shapeFlag?u.pendingId=p.component.suspenseId:u.pendingId=suspenseId++,c(null,p,u.hiddenContainer,null,r,u,o,i,a),u.deps<=0)u.resolve();else{const{timeout:e,pendingId:t}=u;e>0?setTimeout(()=>{u.pendingId===t&&u.fallback(h)},e):0===e&&u.fallback(h)}}function createSuspenseBoundary(e,t,n,s,r,o,i,a,c,l,d=!1){const{p:u,m:p,um:h,n:f,o:{parentNode:m,remove:g}}=l;let y;const v=isVNodeSuspensible(e);v&&t&&t.pendingBranch&&(y=t.pendingId,t.deps++);const S=e.props?toNumber(e.props.timeout):void 0,_=o,b={vnode:e,parent:t,parentComponent:n,namespace:i,container:s,hiddenContainer:r,deps:0,pendingId:suspenseId++,timeout:"number"==typeof S?S:-1,activeBranch:null,pendingBranch:null,isInFallback:!d,isHydrating:d,isUnmounted:!1,effects:[],resolve(e=!1,n=!1){const{vnode:s,activeBranch:r,pendingBranch:i,pendingId:a,effects:c,parentComponent:l,container:d}=b;let u=!1;b.isHydrating?b.isHydrating=!1:e||(u=r&&i.transition&&"out-in"===i.transition.mode,u&&(r.transition.afterLeave=()=>{a===b.pendingId&&(p(i,d,o===_?f(r):o,0),queuePostFlushCb(c))}),r&&(m(r.el)===d&&(o=f(r)),h(r,l,b,!0)),u||p(i,d,o,0)),setActiveBranch(b,i),b.pendingBranch=null,b.isInFallback=!1;let g=b.parent,S=!1;for(;g;){if(g.pendingBranch){g.effects.push(...c),S=!0;break}g=g.parent}S||u||queuePostFlushCb(c),b.effects=[],v&&t&&t.pendingBranch&&y===t.pendingId&&(t.deps--,0!==t.deps||n||t.resolve()),triggerEvent(s,"onResolve")},fallback(e){if(!b.pendingBranch)return;const{vnode:t,activeBranch:n,parentComponent:s,container:r,namespace:o}=b;triggerEvent(t,"onFallback");const i=f(n),l=()=>{b.isInFallback&&(u(null,e,r,i,s,null,o,a,c),setActiveBranch(b,e))},d=e.transition&&"out-in"===e.transition.mode;d&&(n.transition.afterLeave=l),b.isInFallback=!0,h(n,s,null,!0),d||l()},move(e,t,n){b.activeBranch&&p(b.activeBranch,e,t,n),b.container=e},next:()=>b.activeBranch&&f(b.activeBranch),registerDep(e,t,n){const s=!!b.pendingBranch;s&&b.deps++;const r=e.vnode.el;e.asyncDep.catch(t=>{handleError(t,e,0)}).then(o=>{if(e.isUnmounted||b.isUnmounted||b.pendingId!==e.suspenseId)return;e.asyncResolved=!0;const{vnode:a}=e;handleSetupResult(e,o,!1),r&&(a.el=r);const c=!r&&e.subTree.el;t(e,a,m(r||e.subTree.el),r?null:f(e.subTree),b,i,n),c&&g(c),updateHOCHostEl(e,a.el),s&&0===--b.deps&&b.resolve()})},unmount(e,t){b.isUnmounted=!0,b.activeBranch&&h(b.activeBranch,n,e,t),b.pendingBranch&&h(b.pendingBranch,n,e,t)}};return b}function hydrateSuspense(e,t,n,s,r,o,i,a,c){const l=t.suspense=createSuspenseBoundary(t,s,n,e.parentNode,document.createElement("div"),null,r,o,i,a,!0),d=c(e,l.pendingBranch=t.ssContent,n,l,o,i);return 0===l.deps&&l.resolve(!1,!0),d}function normalizeSuspenseChildren(e){const{shapeFlag:t,children:n}=e,s=32&t;e.ssContent=normalizeSuspenseSlot(s?n.default:n),e.ssFallback=s?normalizeSuspenseSlot(n.fallback):createVNode(Comment)}function normalizeSuspenseSlot(e){let t;if(isFunction(e)){const n=isBlockTreeEnabled&&e._c;n&&(e._d=!1,openBlock()),e=e(),n&&(e._d=!0,t=currentBlock,closeBlock())}if(isArray(e)){const t=filterSingleRoot(e);e=t}return e=normalizeVNode(e),t&&!e.dynamicChildren&&(e.dynamicChildren=t.filter(t=>t!==e)),e}function queueEffectWithSuspense(e,t){t&&t.pendingBranch?isArray(e)?t.effects.push(...e):t.effects.push(e):queuePostFlushCb(e)}function setActiveBranch(e,t){e.activeBranch=t;const{vnode:n,parentComponent:s}=e;let r=t.el;for(;!r&&t.component;)r=(t=t.component.subTree).el;n.el=r,s&&s.subTree===n&&(s.vnode.el=r,updateHOCHostEl(s,r))}function isVNodeSuspensible(e){const t=e.props&&e.props.suspensible;return null!=t&&!1!==t}const Fragment=Symbol.for("v-fgt"),Text=Symbol.for("v-txt"),Comment=Symbol.for("v-cmt"),Static=Symbol.for("v-stc"),blockStack=[];let currentBlock=null;function openBlock(e=!1){blockStack.push(currentBlock=e?null:[])}function closeBlock(){blockStack.pop(),currentBlock=blockStack[blockStack.length-1]||null}let isBlockTreeEnabled=1;function setBlockTracking(e,t=!1){isBlockTreeEnabled+=e,e<0&&currentBlock&&t&&(currentBlock.hasOnce=!0)}function setupBlock(e){return e.dynamicChildren=isBlockTreeEnabled>0?currentBlock||EMPTY_ARR:null,closeBlock(),isBlockTreeEnabled>0&&currentBlock&&currentBlock.push(e),e}function createElementBlock(e,t,n,s,r,o){return setupBlock(createBaseVNode(e,t,n,s,r,o,!0))}function createBlock(e,t,n,s,r){return setupBlock(createVNode(e,t,n,s,r,!0))}function isVNode(e){return!!e&&!0===e.__v_isVNode}function isSameVNodeType(e,t){return e.type===t.type&&e.key===t.key}function transformVNodeArgs(e){}const normalizeKey=({key:e})=>null!=e?e:null,normalizeRef=({ref:e,ref_key:t,ref_for:n})=>("number"==typeof e&&(e=""+e),null!=e?isString(e)||isRef(e)||isFunction(e)?{i:currentRenderingInstance,r:e,k:t,f:!!n}:e:null);function createBaseVNode(e,t=null,n=null,s=0,r=null,o=(e===Fragment?0:1),i=!1,a=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&normalizeKey(t),ref:t&&normalizeRef(t),scopeId:currentScopeId,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:currentRenderingInstance};return a?(normalizeChildren(c,n),128&o&&e.normalize(c)):n&&(c.shapeFlag|=isString(n)?8:16),isBlockTreeEnabled>0&&!i&&currentBlock&&(c.patchFlag>0||6&o)&&32!==c.patchFlag&&currentBlock.push(c),c}const createVNode=_createVNode;function _createVNode(e,t=null,n=null,s=0,r=null,o=!1){if(e&&e!==NULL_DYNAMIC_COMPONENT||(e=Comment),isVNode(e)){const s=cloneVNode(e,t,!0);return n&&normalizeChildren(s,n),isBlockTreeEnabled>0&&!o&&currentBlock&&(6&s.shapeFlag?currentBlock[currentBlock.indexOf(e)]=s:currentBlock.push(s)),s.patchFlag=-2,s}if(isClassComponent(e)&&(e=e.__vccOpts),t){t=guardReactiveProps(t);let{class:e,style:n}=t;e&&!isString(e)&&(t.class=normalizeClass(e)),isObject(n)&&(isProxy(n)&&!isArray(n)&&(n=extend({},n)),t.style=normalizeStyle(n))}return createBaseVNode(e,t,n,s,r,isString(e)?1:isSuspense(e)?128:isTeleport(e)?64:isObject(e)?4:isFunction(e)?2:0,o,!0)}function guardReactiveProps(e){return e?isProxy(e)||isInternalObject(e)?extend({},e):e:null}function cloneVNode(e,t,n=!1,s=!1){const{props:r,ref:o,patchFlag:i,children:a,transition:c}=e,l=t?mergeProps(r||{},t):r,d={__v_isVNode:!0,__v_skip:!0,type:e.type,props:l,key:l&&normalizeKey(l),ref:t&&t.ref?n&&o?isArray(o)?o.concat(normalizeRef(t)):[o,normalizeRef(t)]:normalizeRef(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Fragment?-1===i?16:16|i:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&cloneVNode(e.ssContent),ssFallback:e.ssFallback&&cloneVNode(e.ssFallback),placeholder:e.placeholder,el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&s&&setTransitionHooks(d,c.clone(d)),d}function createTextVNode(e=" ",t=0){return createVNode(Text,null,e,t)}function createStaticVNode(e,t){const n=createVNode(Static,null,e);return n.staticCount=t,n}function createCommentVNode(e="",t=!1){return t?(openBlock(),createBlock(Comment,null,e)):createVNode(Comment,null,e)}function normalizeVNode(e){return null==e||"boolean"==typeof e?createVNode(Comment):isArray(e)?createVNode(Fragment,null,e.slice()):isVNode(e)?cloneIfMounted(e):createVNode(Text,null,String(e))}function cloneIfMounted(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:cloneVNode(e)}function normalizeChildren(e,t){let n=0;const{shapeFlag:s}=e;if(null==t)t=null;else if(isArray(t))n=16;else if("object"==typeof t){if(65&s){const n=t.default;return void(n&&(n._c&&(n._d=!1),normalizeChildren(e,n()),n._c&&(n._d=!0)))}{n=32;const s=t._;s||isInternalObject(t)?3===s&&currentRenderingInstance&&(1===currentRenderingInstance.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=currentRenderingInstance}}else isFunction(t)?(t={default:t,_ctx:currentRenderingInstance},n=32):(t=String(t),64&s?(n=16,t=[createTextVNode(t)]):n=8);e.children=t,e.shapeFlag|=n}function mergeProps(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const e in s)if("class"===e)t.class!==s.class&&(t.class=normalizeClass([t.class,s.class]));else if("style"===e)t.style=normalizeStyle([t.style,s.style]);else if(isOn(e)){const n=t[e],r=s[e];!r||n===r||isArray(n)&&n.includes(r)||(t[e]=n?[].concat(n,r):r)}else""!==e&&(t[e]=s[e])}return t}function invokeVNodeHook(e,t,n,s=null){callWithAsyncErrorHandling(e,t,7,[n,s])}const emptyAppContext=createAppContext();let uid=0;function createComponentInstance(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||emptyAppContext,o={uid:uid++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new EffectScope(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:normalizePropsOptions(s,r),emitsOptions:normalizeEmitsOptions(s,r),emit:null,emitted:null,propsDefaults:EMPTY_OBJ,inheritAttrs:s.inheritAttrs,ctx:EMPTY_OBJ,data:EMPTY_OBJ,props:EMPTY_OBJ,attrs:EMPTY_OBJ,slots:EMPTY_OBJ,refs:EMPTY_OBJ,setupState:EMPTY_OBJ,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=emit.bind(null,o),e.ce&&e.ce(o),o}let currentInstance=null;const getCurrentInstance=()=>currentInstance||currentRenderingInstance;let internalSetCurrentInstance,setInSSRSetupState;{const e=getGlobalThis(),t=(t,n)=>{let s;return(s=e[t])||(s=e[t]=[]),s.push(n),e=>{s.length>1?s.forEach(t=>t(e)):s[0](e)}};internalSetCurrentInstance=t("__VUE_INSTANCE_SETTERS__",e=>currentInstance=e),setInSSRSetupState=t("__VUE_SSR_SETTERS__",e=>isInSSRComponentSetup=e)}const setCurrentInstance=e=>{const t=currentInstance;return internalSetCurrentInstance(e),e.scope.on(),()=>{e.scope.off(),internalSetCurrentInstance(t)}},unsetCurrentInstance=()=>{currentInstance&&currentInstance.scope.off(),internalSetCurrentInstance(null)};function isStatefulComponent(e){return 4&e.vnode.shapeFlag}let isInSSRComponentSetup=!1,compile$1,installWithProxy;function setupComponent(e,t=!1,n=!1){t&&setInSSRSetupState(t);const{props:s,children:r}=e.vnode,o=isStatefulComponent(e);initProps(e,s,o,t),initSlots(e,r,n||t);const i=o?setupStatefulComponent(e,t):void 0;return t&&setInSSRSetupState(!1),i}function setupStatefulComponent(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,PublicInstanceProxyHandlers);const{setup:s}=n;if(s){pauseTracking();const n=e.setupContext=s.length>1?createSetupContext(e):null,r=setCurrentInstance(e),o=callWithErrorHandling(s,e,0,[e.props,n]),i=isPromise(o);if(resetTracking(),r(),!i&&!e.sp||isAsyncWrapper(e)||markAsyncBoundary(e),i){if(o.then(unsetCurrentInstance,unsetCurrentInstance),t)return o.then(n=>{handleSetupResult(e,n,t)}).catch(t=>{handleError(t,e,0)});e.asyncDep=o}else handleSetupResult(e,o,t)}else finishComponentSetup(e,t)}function handleSetupResult(e,t,n){isFunction(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:isObject(t)&&(e.setupState=proxyRefs(t)),finishComponentSetup(e,n)}function registerRuntimeCompiler(e){compile$1=e,installWithProxy=e=>{e.render._rc&&(e.withProxy=new Proxy(e.ctx,RuntimeCompiledPublicInstanceProxyHandlers))}}const isRuntimeOnly=()=>!compile$1;function finishComponentSetup(e,t,n){const s=e.type;if(!e.render){if(!t&&compile$1&&!s.render){const t=s.template||resolveMergedOptions(e).template;if(t){const{isCustomElement:n,compilerOptions:r}=e.appContext.config,{delimiters:o,compilerOptions:i}=s,a=extend(extend({isCustomElement:n,delimiters:o},r),i);s.render=compile$1(t,a)}}e.render=s.render||NOOP,installWithProxy&&installWithProxy(e)}{const t=setCurrentInstance(e);pauseTracking();try{applyOptions(e)}finally{resetTracking(),t()}}}const attrsProxyHandlers={get:(e,t)=>(track(e,"get",""),e[t])};function createSetupContext(e){const t=t=>{e.exposed=t||{}};return{attrs:new Proxy(e.attrs,attrsProxyHandlers),slots:e.slots,emit:e.emit,expose:t}}function getComponentPublicInstance(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(proxyRefs(markRaw(e.exposed)),{get:(t,n)=>n in t?t[n]:n in publicPropertiesMap?publicPropertiesMap[n](e):void 0,has:(e,t)=>t in e||t in publicPropertiesMap})):e.proxy}const classifyRE=/(?:^|[-_])(\w)/g,classify=e=>e.replace(classifyRE,e=>e.toUpperCase()).replace(/[-_]/g,"");function getComponentName(e,t=!0){return isFunction(e)?e.displayName||e.name:e.name||t&&e.__name}function formatComponentName(e,t,n=!1){let s=getComponentName(t);if(!s&&t.__file){const e=t.__file.match(/([^/\\]+)\.\w+$/);e&&(s=e[1])}if(!s&&e&&e.parent){const n=e=>{for(const n in e)if(e[n]===t)return n};s=n(e.components||e.parent.type.components)||n(e.appContext.components)}return s?classify(s):n?"App":"Anonymous"}function isClassComponent(e){return isFunction(e)&&"__vccOpts"in e}const computed=(e,t)=>computed$1(e,t,isInSSRComponentSetup);function h(e,t,n){const s=arguments.length;return 2===s?isObject(t)&&!isArray(t)?isVNode(t)?createVNode(e,null,[t]):createVNode(e,t):createVNode(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):3===s&&isVNode(n)&&(n=[n]),createVNode(e,t,n))}function initCustomFormatter(){}function withMemo(e,t,n,s){const r=n[s];if(r&&isMemoSame(r,e))return r;const o=t();return o.memo=e.slice(),o.cacheIndex=s,n[s]=o}function isMemoSame(e,t){const n=e.memo;if(n.length!=t.length)return!1;for(let s=0;s<n.length;s++)if(hasChanged(n[s],t[s]))return!1;return isBlockTreeEnabled>0&&currentBlock&&currentBlock.push(e),!0}const version="3.5.18",warn=NOOP,ErrorTypeStrings=ErrorTypeStrings$1,devtools=devtools$1,setDevtoolsHook=setDevtoolsHook$1,_ssrUtils={createComponentInstance:createComponentInstance,setupComponent:setupComponent,renderComponentRoot:renderComponentRoot,setCurrentRenderingInstance:setCurrentRenderingInstance,isVNode:isVNode,normalizeVNode:normalizeVNode,getComponentPublicInstance:getComponentPublicInstance,ensureValidVNode:ensureValidVNode,pushWarningContext:pushWarningContext,popWarningContext:popWarningContext},ssrUtils=_ssrUtils,resolveFilter=null,compatUtils=null,DeprecationTypes=null;
/**
* @vue/runtime-dom v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/
let policy;const tt="undefined"!=typeof window&&window.trustedTypes;if(tt)try{policy=tt.createPolicy("vue",{createHTML:e=>e})}catch(e){}const unsafeToTrustedHTML=policy?e=>policy.createHTML(e):e=>e,svgNS="http://www.w3.org/2000/svg",mathmlNS="http://www.w3.org/1998/Math/MathML",doc="undefined"!=typeof document?document:null,templateContainer=doc&&doc.createElement("template"),nodeOps={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r="svg"===t?doc.createElementNS(svgNS,e):"mathml"===t?doc.createElementNS(mathmlNS,e):n?doc.createElement(e,{is:n}):doc.createElement(e);return"select"===e&&s&&null!=s.multiple&&r.setAttribute("multiple",s.multiple),r},createText:e=>doc.createTextNode(e),createComment:e=>doc.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>doc.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,o){const i=n?n.previousSibling:t.lastChild;if(r&&(r===o||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),r!==o&&(r=r.nextSibling););else{templateContainer.innerHTML=unsafeToTrustedHTML("svg"===s?`<svg>${e}</svg>`:"mathml"===s?`<math>${e}</math>`:e);const r=templateContainer.content;if("svg"===s||"mathml"===s){const e=r.firstChild;for(;e.firstChild;)r.appendChild(e.firstChild);r.removeChild(e)}t.insertBefore(r,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},TRANSITION$1="transition",ANIMATION="animation",vtcKey=Symbol("_vtc"),DOMTransitionPropsValidators={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},TransitionPropsValidators=extend({},BaseTransitionPropsValidators,DOMTransitionPropsValidators),decorate$1=e=>(e.displayName="Transition",e.props=TransitionPropsValidators,e),Transition=decorate$1((e,{slots:t})=>h(BaseTransition,resolveTransitionProps(e),t)),callHook=(e,t=[])=>{isArray(e)?e.forEach(e=>e(...t)):e&&e(...t)},hasExplicitCallback=e=>!!e&&(isArray(e)?e.some(e=>e.length>1):e.length>1);function resolveTransitionProps(e){const t={};for(const x in e)x in DOMTransitionPropsValidators||(t[x]=e[x]);if(!1===e.css)return t;const{name:n="v",type:s,duration:r,enterFromClass:o=`${n}-enter-from`,enterActiveClass:i=`${n}-enter-active`,enterToClass:a=`${n}-enter-to`,appearFromClass:c=o,appearActiveClass:l=i,appearToClass:d=a,leaveFromClass:u=`${n}-leave-from`,leaveActiveClass:p=`${n}-leave-active`,leaveToClass:h=`${n}-leave-to`}=e,f=normalizeDuration(r),m=f&&f[0],g=f&&f[1],{onBeforeEnter:y,onEnter:v,onEnterCancelled:S,onLeave:_,onLeaveCancelled:b,onBeforeAppear:E=y,onAppear:C=v,onAppearCancelled:T=S}=t,N=(e,t,n,s)=>{e._enterCancelled=s,removeTransitionClass(e,t?d:a),removeTransitionClass(e,t?l:i),n&&n()},A=(e,t)=>{e._isLeaving=!1,removeTransitionClass(e,u),removeTransitionClass(e,h),removeTransitionClass(e,p),t&&t()},R=e=>(t,n)=>{const r=e?C:v,i=()=>N(t,e,n);callHook(r,[t,i]),nextFrame(()=>{removeTransitionClass(t,e?c:o),addTransitionClass(t,e?d:a),hasExplicitCallback(r)||whenTransitionEnds(t,s,m,i)})};return extend(t,{onBeforeEnter(e){callHook(y,[e]),addTransitionClass(e,o),addTransitionClass(e,i)},onBeforeAppear(e){callHook(E,[e]),addTransitionClass(e,c),addTransitionClass(e,l)},onEnter:R(!1),onAppear:R(!0),onLeave(e,t){e._isLeaving=!0;const n=()=>A(e,t);addTransitionClass(e,u),e._enterCancelled?(addTransitionClass(e,p),forceReflow()):(forceReflow(),addTransitionClass(e,p)),nextFrame(()=>{e._isLeaving&&(removeTransitionClass(e,u),addTransitionClass(e,h),hasExplicitCallback(_)||whenTransitionEnds(e,s,g,n))}),callHook(_,[e,n])},onEnterCancelled(e){N(e,!1,void 0,!0),callHook(S,[e])},onAppearCancelled(e){N(e,!0,void 0,!0),callHook(T,[e])},onLeaveCancelled(e){A(e),callHook(b,[e])}})}function normalizeDuration(e){if(null==e)return null;if(isObject(e))return[NumberOf(e.enter),NumberOf(e.leave)];{const t=NumberOf(e);return[t,t]}}function NumberOf(e){return toNumber(e)}function addTransitionClass(e,t){t.split(/\s+/).forEach(t=>t&&e.classList.add(t)),(e[vtcKey]||(e[vtcKey]=new Set)).add(t)}function removeTransitionClass(e,t){t.split(/\s+/).forEach(t=>t&&e.classList.remove(t));const n=e[vtcKey];n&&(n.delete(t),n.size||(e[vtcKey]=void 0))}function nextFrame(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let endId=0;function whenTransitionEnds(e,t,n,s){const r=e._endId=++endId,o=()=>{r===e._endId&&s()};if(null!=n)return setTimeout(o,n);const{type:i,timeout:a,propCount:c}=getTransitionInfo(e,t);if(!i)return s();const l=i+"end";let d=0;const u=()=>{e.removeEventListener(l,p),o()},p=t=>{t.target===e&&++d>=c&&u()};setTimeout(()=>{d<c&&u()},a+1),e.addEventListener(l,p)}function getTransitionInfo(e,t){const n=window.getComputedStyle(e),s=e=>(n[e]||"").split(", "),r=s(`${TRANSITION$1}Delay`),o=s(`${TRANSITION$1}Duration`),i=getTimeout(r,o),a=s(`${ANIMATION}Delay`),c=s(`${ANIMATION}Duration`),l=getTimeout(a,c);let d=null,u=0,p=0;t===TRANSITION$1?i>0&&(d=TRANSITION$1,u=i,p=o.length):t===ANIMATION?l>0&&(d=ANIMATION,u=l,p=c.length):(u=Math.max(i,l),d=u>0?i>l?TRANSITION$1:ANIMATION:null,p=d?d===TRANSITION$1?o.length:c.length:0);return{type:d,timeout:u,propCount:p,hasTransform:d===TRANSITION$1&&/\b(transform|all)(,|$)/.test(s(`${TRANSITION$1}Property`).toString())}}function getTimeout(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((t,n)=>toMs(t)+toMs(e[n])))}function toMs(e){return"auto"===e?0:1e3*Number(e.slice(0,-1).replace(",","."))}function forceReflow(){return document.body.offsetHeight}function patchClass(e,t,n){const s=e[vtcKey];s&&(t=(t?[t,...s]:[...s]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const vShowOriginalDisplay=Symbol("_vod"),vShowHidden=Symbol("_vsh"),vShow={beforeMount(e,{value:t},{transition:n}){e[vShowOriginalDisplay]="none"===e.style.display?"":e.style.display,n&&t?n.beforeEnter(e):setDisplay(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:s}){!t!=!n&&(s?t?(s.beforeEnter(e),setDisplay(e,!0),s.enter(e)):s.leave(e,()=>{setDisplay(e,!1)}):setDisplay(e,t))},beforeUnmount(e,{value:t}){setDisplay(e,t)}};function setDisplay(e,t){e.style.display=t?e[vShowOriginalDisplay]:"none",e[vShowHidden]=!t}function initVShowForSSR(){vShow.getSSRProps=({value:e})=>{if(!e)return{style:{display:"none"}}}}const CSS_VAR_TEXT=Symbol("");function useCssVars(e){const t=getCurrentInstance();if(!t)return;const n=t.ut=(n=e(t.proxy))=>{Array.from(document.querySelectorAll(`[data-v-owner="${t.uid}"]`)).forEach(e=>setVarsOnNode(e,n))},s=()=>{const s=e(t.proxy);t.ce?setVarsOnNode(t.ce,s):setVarsOnVNode(t.subTree,s),n(s)};onBeforeUpdate(()=>{queuePostFlushCb(s)}),onMounted(()=>{watch(s,NOOP,{flush:"post"});const e=new MutationObserver(s);e.observe(t.subTree.el.parentNode,{childList:!0}),onUnmounted(()=>e.disconnect())})}function setVarsOnVNode(e,t){if(128&e.shapeFlag){const n=e.suspense;e=n.activeBranch,n.pendingBranch&&!n.isHydrating&&n.effects.push(()=>{setVarsOnVNode(n.activeBranch,t)})}for(;e.component;)e=e.component.subTree;if(1&e.shapeFlag&&e.el)setVarsOnNode(e.el,t);else if(e.type===Fragment)e.children.forEach(e=>setVarsOnVNode(e,t));else if(e.type===Static){let{el:n,anchor:s}=e;for(;n&&(setVarsOnNode(n,t),n!==s);)n=n.nextSibling}}function setVarsOnNode(e,t){if(1===e.nodeType){const n=e.style;let s="";for(const e in t){const r=normalizeCssVarValue(t[e]);n.setProperty(`--${e}`,r),s+=`--${e}: ${r};`}n[CSS_VAR_TEXT]=s}}const displayRE=/(^|;)\s*display\s*:/;function patchStyle(e,t,n){const s=e.style,r=isString(n);let o=!1;if(n&&!r){if(t)if(isString(t))for(const e of t.split(";")){const t=e.slice(0,e.indexOf(":")).trim();null==n[t]&&setStyle(s,t,"")}else for(const e in t)null==n[e]&&setStyle(s,e,"");for(const e in n)"display"===e&&(o=!0),setStyle(s,e,n[e])}else if(r){if(t!==n){const e=s[CSS_VAR_TEXT];e&&(n+=";"+e),s.cssText=n,o=displayRE.test(n)}}else t&&e.removeAttribute("style");vShowOriginalDisplay in e&&(e[vShowOriginalDisplay]=o?s.display:"",e[vShowHidden]&&(s.display="none"))}const importantRE=/\s*!important$/;function setStyle(e,t,n){if(isArray(n))n.forEach(n=>setStyle(e,t,n));else if(null==n&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=autoPrefix(e,t);importantRE.test(n)?e.setProperty(hyphenate(s),n.replace(importantRE,""),"important"):e[s]=n}}const prefixes=["Webkit","Moz","ms"],prefixCache={};function autoPrefix(e,t){const n=prefixCache[t];if(n)return n;let s=camelize(t);if("filter"!==s&&s in e)return prefixCache[t]=s;s=capitalize(s);for(let r=0;r<prefixes.length;r++){const n=prefixes[r]+s;if(n in e)return prefixCache[t]=n}return t}const xlinkNS="http://www.w3.org/1999/xlink";function patchAttr(e,t,n,s,r,o=isSpecialBooleanAttr(t)){s&&t.startsWith("xlink:")?null==n?e.removeAttributeNS(xlinkNS,t.slice(6,t.length)):e.setAttributeNS(xlinkNS,t,n):null==n||o&&!includeBooleanAttr(n)?e.removeAttribute(t):e.setAttribute(t,o?"":isSymbol(n)?String(n):n)}function patchDOMProp(t,n,s,r,o){if("innerHTML"===n||"textContent"===n)return void(null!=s&&(t[n]="innerHTML"===n?unsafeToTrustedHTML(s):s));const i=t.tagName;if("value"===n&&"PROGRESS"!==i&&!i.includes("-")){const e="OPTION"===i?t.getAttribute("value")||"":t.value,r=null==s?"checkbox"===t.type?"on":"":String(s);return e===r&&"_value"in t||(t.value=r),null==s&&t.removeAttribute(n),void(t._value=s)}let a=!1;if(""===s||null==s){const e=typeof t[n];"boolean"===e?s=includeBooleanAttr(s):null==s&&"string"===e?(s="",a=!0):"number"===e&&(s=0,a=!0)}try{t[n]=s}catch(e){}a&&t.removeAttribute(o||n)}function addEventListener(e,t,n,s){e.addEventListener(t,n,s)}function removeEventListener(e,t,n,s){e.removeEventListener(t,n,s)}const veiKey=Symbol("_vei");function patchEvent(e,t,n,s,r=null){const o=e[veiKey]||(e[veiKey]={}),i=o[t];if(s&&i)i.value=s;else{const[n,a]=parseName(t);if(s){addEventListener(e,n,o[t]=createInvoker(s,r),a)}else i&&(removeEventListener(e,n,i,a),o[t]=void 0)}}const optionsModifierRE=/(?:Once|Passive|Capture)$/;function parseName(e){let t;if(optionsModifierRE.test(e)){let n;for(t={};n=e.match(optionsModifierRE);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[":"===e[2]?e.slice(3):hyphenate(e.slice(2)),t]}let cachedNow=0;const p=Promise.resolve(),getNow=()=>cachedNow||(p.then(()=>cachedNow=0),cachedNow=Date.now());function createInvoker(e,t){const n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();callWithAsyncErrorHandling(patchStopImmediatePropagation(e,n.value),t,5,[e])};return n.value=e,n.attached=getNow(),n}function patchStopImmediatePropagation(e,t){if(isArray(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(e=>t=>!t._stopped&&e&&e(t))}return t}const isNativeOn=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,patchProp=(e,t,n,s,r,o)=>{const i="svg"===r;"class"===t?patchClass(e,s,i):"style"===t?patchStyle(e,n,s):isOn(t)?isModelListener(t)||patchEvent(e,t,n,s,o):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):shouldSetAsProp(e,t,s,i))?(patchDOMProp(e,t,s),e.tagName.includes("-")||"value"!==t&&"checked"!==t&&"selected"!==t||patchAttr(e,t,s,i,o,"value"!==t)):!e._isVueCE||!/[A-Z]/.test(t)&&isString(s)?("true-value"===t?e._trueValue=s:"false-value"===t&&(e._falseValue=s),patchAttr(e,t,s,i)):patchDOMProp(e,camelize(t),s,o,t)};function shouldSetAsProp(e,t,n,s){if(s)return"innerHTML"===t||"textContent"===t||!!(t in e&&isNativeOn(t)&&isFunction(n));if("spellcheck"===t||"draggable"===t||"translate"===t||"autocorrect"===t)return!1;if("form"===t)return!1;if("list"===t&&"INPUT"===e.tagName)return!1;if("type"===t&&"TEXTAREA"===e.tagName)return!1;if("width"===t||"height"===t){const t=e.tagName;if("IMG"===t||"VIDEO"===t||"CANVAS"===t||"SOURCE"===t)return!1}return(!isNativeOn(t)||!isString(n))&&t in e}const REMOVAL={};
/*! #__NO_SIDE_EFFECTS__ */function defineCustomElement(e,t,n){const s=defineComponent(e,t);isPlainObject(s)&&extend(s,t);class r extends VueElement{constructor(e){super(s,e,n)}}return r.def=s,r}
/*! #__NO_SIDE_EFFECTS__ */const defineSSRCustomElement=(e,t)=>defineCustomElement(e,t,createSSRApp),BaseClass="undefined"!=typeof HTMLElement?HTMLElement:class{};class VueElement extends BaseClass{constructor(e,t={},n=createApp){super(),this._def=e,this._props=t,this._createApp=n,this._isVueCE=!0,this._instance=null,this._app=null,this._nonce=this._def.nonce,this._connected=!1,this._resolved=!1,this._numberProps=null,this._styleChildren=new WeakSet,this._ob=null,this.shadowRoot&&n!==createApp?this._root=this.shadowRoot:!1!==e.shadowRoot?(this.attachShadow({mode:"open"}),this._root=this.shadowRoot):this._root=this}connectedCallback(){if(!this.isConnected)return;this.shadowRoot||this._resolved||this._parseSlots(),this._connected=!0;let e=this;for(;e=e&&(e.parentNode||e.host);)if(e instanceof VueElement){this._parent=e;break}this._instance||(this._resolved?this._mount(this._def):e&&e._pendingResolve?this._pendingResolve=e._pendingResolve.then(()=>{this._pendingResolve=void 0,this._resolveDef()}):this._resolveDef())}_setParent(e=this._parent){e&&(this._instance.parent=e._instance,this._inheritParentContext(e))}_inheritParentContext(e=this._parent){e&&this._app&&Object.setPrototypeOf(this._app._context.provides,e._instance.provides)}disconnectedCallback(){this._connected=!1,nextTick(()=>{this._connected||(this._ob&&(this._ob.disconnect(),this._ob=null),this._app&&this._app.unmount(),this._instance&&(this._instance.ce=void 0),this._app=this._instance=null)})}_resolveDef(){if(this._pendingResolve)return;for(let n=0;n<this.attributes.length;n++)this._setAttr(this.attributes[n].name);this._ob=new MutationObserver(e=>{for(const t of e)this._setAttr(t.attributeName)}),this._ob.observe(this,{attributes:!0});const e=(e,t=!1)=>{this._resolved=!0,this._pendingResolve=void 0;const{props:n,styles:s}=e;let r;if(n&&!isArray(n))for(const o in n){const e=n[o];(e===Number||e&&e.type===Number)&&(o in this._props&&(this._props[o]=toNumber(this._props[o])),(r||(r=Object.create(null)))[camelize(o)]=!0)}this._numberProps=r,this._resolveProps(e),this.shadowRoot&&this._applyStyles(s),this._mount(e)},t=this._def.__asyncLoader;t?this._pendingResolve=t().then(t=>{t.configureApp=this._def.configureApp,e(this._def=t,!0)}):e(this._def)}_mount(e){this._app=this._createApp(e),this._inheritParentContext(),e.configureApp&&e.configureApp(this._app),this._app._ceVNode=this._createVNode(),this._app.mount(this._root);const t=this._instance&&this._instance.exposed;if(t)for(const n in t)hasOwn(this,n)||Object.defineProperty(this,n,{get:()=>unref(t[n])})}_resolveProps(e){const{props:t}=e,n=isArray(t)?t:Object.keys(t||{});for(const s of Object.keys(this))"_"!==s[0]&&n.includes(s)&&this._setProp(s,this[s]);for(const s of n.map(camelize))Object.defineProperty(this,s,{get(){return this._getProp(s)},set(e){this._setProp(s,e,!0,!0)}})}_setAttr(e){if(e.startsWith("data-v-"))return;const t=this.hasAttribute(e);let n=t?this.getAttribute(e):REMOVAL;const s=camelize(e);t&&this._numberProps&&this._numberProps[s]&&(n=toNumber(n)),this._setProp(s,n,!1,!0)}_getProp(e){return this._props[e]}_setProp(e,t,n=!0,s=!1){if(t!==this._props[e]&&(t===REMOVAL?delete this._props[e]:(this._props[e]=t,"key"===e&&this._app&&(this._app._ceVNode.key=t)),s&&this._instance&&this._update(),n)){const n=this._ob;n&&n.disconnect(),!0===t?this.setAttribute(hyphenate(e),""):"string"==typeof t||"number"==typeof t?this.setAttribute(hyphenate(e),t+""):t||this.removeAttribute(hyphenate(e)),n&&n.observe(this,{attributes:!0})}}_update(){const e=this._createVNode();this._app&&(e.appContext=this._app._context),render(e,this._root)}_createVNode(){const e={};this.shadowRoot||(e.onVnodeMounted=e.onVnodeUpdated=this._renderSlots.bind(this));const t=createVNode(this._def,extend(e,this._props));return this._instance||(t.ce=e=>{this._instance=e,e.ce=this,e.isCE=!0;const t=(e,t)=>{this.dispatchEvent(new CustomEvent(e,isPlainObject(t[0])?extend({detail:t},t[0]):{detail:t}))};e.emit=(e,...n)=>{t(e,n),hyphenate(e)!==e&&t(hyphenate(e),n)},this._setParent()}),t}_applyStyles(e,t){if(!e)return;if(t){if(t===this._def||this._styleChildren.has(t))return;this._styleChildren.add(t)}const n=this._nonce;for(let s=e.length-1;s>=0;s--){const t=document.createElement("style");n&&t.setAttribute("nonce",n),t.textContent=e[s],this.shadowRoot.prepend(t)}}_parseSlots(){const e=this._slots={};let t;for(;t=this.firstChild;){const n=1===t.nodeType&&t.getAttribute("slot")||"default";(e[n]||(e[n]=[])).push(t),this.removeChild(t)}}_renderSlots(){const e=(this._teleportTarget||this).querySelectorAll("slot"),t=this._instance.type.__scopeId;for(let n=0;n<e.length;n++){const s=e[n],r=s.getAttribute("name")||"default",o=this._slots[r],i=s.parentNode;if(o)for(const e of o){if(t&&1===e.nodeType){const n=t+"-s",s=document.createTreeWalker(e,1);let r;for(e.setAttribute(n,"");r=s.nextNode();)r.setAttribute(n,"")}i.insertBefore(e,s)}else for(;s.firstChild;)i.insertBefore(s.firstChild,s);i.removeChild(s)}}_injectChildStyle(e){this._applyStyles(e.styles,e)}_removeChildStyle(e){}}function useHost(e){const t=getCurrentInstance(),n=t&&t.ce;return n||null}function useShadowRoot(){const e=useHost();return e&&e.shadowRoot}function useCssModule(e="$style"){{const t=getCurrentInstance();if(!t)return EMPTY_OBJ;const n=t.type.__cssModules;if(!n)return EMPTY_OBJ;const s=n[e];return s||EMPTY_OBJ}}const positionMap=new WeakMap,newPositionMap=new WeakMap,moveCbKey=Symbol("_moveCb"),enterCbKey=Symbol("_enterCb"),decorate=e=>(delete e.props.mode,e),TransitionGroupImpl=decorate({name:"TransitionGroup",props:extend({},TransitionPropsValidators,{tag:String,moveClass:String}),setup(e,{slots:t}){const n=getCurrentInstance(),s=useTransitionState();let r,o;return onUpdated(()=>{if(!r.length)return;const t=e.moveClass||`${e.name||"v"}-move`;if(!hasCSSTransform(r[0].el,n.vnode.el,t))return void(r=[]);r.forEach(callPendingCbs),r.forEach(recordPosition);const s=r.filter(applyTranslation);forceReflow(),s.forEach(e=>{const n=e.el,s=n.style;addTransitionClass(n,t),s.transform=s.webkitTransform=s.transitionDuration="";const r=n[moveCbKey]=e=>{e&&e.target!==n||e&&!/transform$/.test(e.propertyName)||(n.removeEventListener("transitionend",r),n[moveCbKey]=null,removeTransitionClass(n,t))};n.addEventListener("transitionend",r)}),r=[]}),()=>{const i=toRaw(e),a=resolveTransitionProps(i);let c=i.tag||Fragment;if(r=[],o)for(let e=0;e<o.length;e++){const t=o[e];t.el&&t.el instanceof Element&&(r.push(t),setTransitionHooks(t,resolveTransitionHooks(t,a,s,n)),positionMap.set(t,t.el.getBoundingClientRect()))}o=t.default?getTransitionRawChildren(t.default()):[];for(let e=0;e<o.length;e++){const t=o[e];null!=t.key&&setTransitionHooks(t,resolveTransitionHooks(t,a,s,n))}return createVNode(c,null,o)}}}),TransitionGroup=TransitionGroupImpl;function callPendingCbs(e){const t=e.el;t[moveCbKey]&&t[moveCbKey](),t[enterCbKey]&&t[enterCbKey]()}function recordPosition(e){newPositionMap.set(e,e.el.getBoundingClientRect())}function applyTranslation(e){const t=positionMap.get(e),n=newPositionMap.get(e),s=t.left-n.left,r=t.top-n.top;if(s||r){const t=e.el.style;return t.transform=t.webkitTransform=`translate(${s}px,${r}px)`,t.transitionDuration="0s",e}}function hasCSSTransform(e,t,n){const s=e.cloneNode(),r=e[vtcKey];r&&r.forEach(e=>{e.split(/\s+/).forEach(e=>e&&s.classList.remove(e))}),n.split(/\s+/).forEach(e=>e&&s.classList.add(e)),s.style.display="none";const o=1===t.nodeType?t:t.parentNode;o.appendChild(s);const{hasTransform:i}=getTransitionInfo(s);return o.removeChild(s),i}const getModelAssigner=e=>{const t=e.props["onUpdate:modelValue"]||!1;return isArray(t)?e=>invokeArrayFns(t,e):t};function onCompositionStart(e){e.target.composing=!0}function onCompositionEnd(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const assignKey=Symbol("_assign"),vModelText={created(e,{modifiers:{lazy:t,trim:n,number:s}},r){e[assignKey]=getModelAssigner(r);const o=s||r.props&&"number"===r.props.type;addEventListener(e,t?"change":"input",t=>{if(t.target.composing)return;let s=e.value;n&&(s=s.trim()),o&&(s=looseToNumber(s)),e[assignKey](s)}),n&&addEventListener(e,"change",()=>{e.value=e.value.trim()}),t||(addEventListener(e,"compositionstart",onCompositionStart),addEventListener(e,"compositionend",onCompositionEnd),addEventListener(e,"change",onCompositionEnd))},mounted(e,{value:t}){e.value=null==t?"":t},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:r,number:o}},i){if(e[assignKey]=getModelAssigner(i),e.composing)return;const a=null==t?"":t;if((!o&&"number"!==e.type||/^0\d/.test(e.value)?e.value:looseToNumber(e.value))!==a){if(document.activeElement===e&&"range"!==e.type){if(s&&t===n)return;if(r&&e.value.trim()===a)return}e.value=a}}},vModelCheckbox={deep:!0,created(e,t,n){e[assignKey]=getModelAssigner(n),addEventListener(e,"change",()=>{const t=e._modelValue,n=getValue(e),s=e.checked,r=e[assignKey];if(isArray(t)){const e=looseIndexOf(t,n),o=-1!==e;if(s&&!o)r(t.concat(n));else if(!s&&o){const n=[...t];n.splice(e,1),r(n)}}else if(isSet(t)){const e=new Set(t);s?e.add(n):e.delete(n),r(e)}else r(getCheckboxValue(e,s))})},mounted:setChecked,beforeUpdate(e,t,n){e[assignKey]=getModelAssigner(n),setChecked(e,t,n)}};function setChecked(e,{value:t,oldValue:n},s){let r;if(e._modelValue=t,isArray(t))r=looseIndexOf(t,s.props.value)>-1;else if(isSet(t))r=t.has(s.props.value);else{if(t===n)return;r=looseEqual(t,getCheckboxValue(e,!0))}e.checked!==r&&(e.checked=r)}const vModelRadio={created(e,{value:t},n){e.checked=looseEqual(t,n.props.value),e[assignKey]=getModelAssigner(n),addEventListener(e,"change",()=>{e[assignKey](getValue(e))})},beforeUpdate(e,{value:t,oldValue:n},s){e[assignKey]=getModelAssigner(s),t!==n&&(e.checked=looseEqual(t,s.props.value))}},vModelSelect={deep:!0,created(e,{value:t,modifiers:{number:n}},s){const r=isSet(t);addEventListener(e,"change",()=>{const t=Array.prototype.filter.call(e.options,e=>e.selected).map(e=>n?looseToNumber(getValue(e)):getValue(e));e[assignKey](e.multiple?r?new Set(t):t:t[0]),e._assigning=!0,nextTick(()=>{e._assigning=!1})}),e[assignKey]=getModelAssigner(s)},mounted(e,{value:t}){setSelected(e,t)},beforeUpdate(e,t,n){e[assignKey]=getModelAssigner(n)},updated(e,{value:t}){e._assigning||setSelected(e,t)}};function setSelected(e,t){const n=e.multiple,s=isArray(t);if(!n||s||isSet(t)){for(let r=0,o=e.options.length;r<o;r++){const o=e.options[r],i=getValue(o);if(n)if(s){const e=typeof i;o.selected="string"===e||"number"===e?t.some(e=>String(e)===String(i)):looseIndexOf(t,i)>-1}else o.selected=t.has(i);else if(looseEqual(getValue(o),t))return void(e.selectedIndex!==r&&(e.selectedIndex=r))}n||-1===e.selectedIndex||(e.selectedIndex=-1)}}function getValue(e){return"_value"in e?e._value:e.value}function getCheckboxValue(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const vModelDynamic={created(e,t,n){callModelHook(e,t,n,null,"created")},mounted(e,t,n){callModelHook(e,t,n,null,"mounted")},beforeUpdate(e,t,n,s){callModelHook(e,t,n,s,"beforeUpdate")},updated(e,t,n,s){callModelHook(e,t,n,s,"updated")}};function resolveDynamicModel(e,t){switch(e){case"SELECT":return vModelSelect;case"TEXTAREA":return vModelText;default:switch(t){case"checkbox":return vModelCheckbox;case"radio":return vModelRadio;default:return vModelText}}}function callModelHook(e,t,n,s,r){const o=resolveDynamicModel(e.tagName,n.props&&n.props.type)[r];o&&o(e,t,n,s)}function initVModelForSSR(){vModelText.getSSRProps=({value:e})=>({value:e}),vModelRadio.getSSRProps=({value:e},t)=>{if(t.props&&looseEqual(t.props.value,e))return{checked:!0}},vModelCheckbox.getSSRProps=({value:e},t)=>{if(isArray(e)){if(t.props&&looseIndexOf(e,t.props.value)>-1)return{checked:!0}}else if(isSet(e)){if(t.props&&e.has(t.props.value))return{checked:!0}}else if(e)return{checked:!0}},vModelDynamic.getSSRProps=(e,t)=>{if("string"!=typeof t.type)return;const n=resolveDynamicModel(t.type.toUpperCase(),t.props&&t.props.type);return n.getSSRProps?n.getSSRProps(e,t):void 0}}const systemModifiers=["ctrl","shift","alt","meta"],modifierGuards={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&0!==e.button,middle:e=>"button"in e&&1!==e.button,right:e=>"button"in e&&2!==e.button,exact:(e,t)=>systemModifiers.some(n=>e[`${n}Key`]&&!t.includes(n))},withModifiers=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=(n,...s)=>{for(let e=0;e<t.length;e++){const s=modifierGuards[t[e]];if(s&&s(n,t))return}return e(n,...s)})},keyNames={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},withKeys=(e,t)=>{const n=e._withKeys||(e._withKeys={}),s=t.join(".");return n[s]||(n[s]=n=>{if(!("key"in n))return;const s=hyphenate(n.key);return t.some(e=>e===s||keyNames[e]===s)?e(n):void 0})},rendererOptions=extend({patchProp:patchProp},nodeOps);let renderer,enabledHydration=!1;function ensureRenderer(){return renderer||(renderer=createRenderer(rendererOptions))}function ensureHydrationRenderer(){return renderer=enabledHydration?renderer:createHydrationRenderer(rendererOptions),enabledHydration=!0,renderer}const render=(...e)=>{ensureRenderer().render(...e)},hydrate=(...e)=>{ensureHydrationRenderer().hydrate(...e)},createApp=(...e)=>{const t=ensureRenderer().createApp(...e),{mount:n}=t;return t.mount=e=>{const s=normalizeContainer(e);if(!s)return;const r=t._component;isFunction(r)||r.render||r.template||(r.template=s.innerHTML),1===s.nodeType&&(s.textContent="");const o=n(s,!1,resolveRootNamespace(s));return s instanceof Element&&(s.removeAttribute("v-cloak"),s.setAttribute("data-v-app","")),o},t},createSSRApp=(...e)=>{const t=ensureHydrationRenderer().createApp(...e),{mount:n}=t;return t.mount=e=>{const t=normalizeContainer(e);if(t)return n(t,!0,resolveRootNamespace(t))},t};function resolveRootNamespace(e){return e instanceof SVGElement?"svg":"function"==typeof MathMLElement&&e instanceof MathMLElement?"mathml":void 0}function normalizeContainer(e){if(isString(e)){return document.querySelector(e)}return e}let ssrDirectiveInitialized=!1;const initDirectivesForSSR=()=>{ssrDirectiveInitialized||(ssrDirectiveInitialized=!0,initVModelForSSR(),initVShowForSSR())},runtimeDom=Object.freeze(Object.defineProperty({__proto__:null,BaseTransition:BaseTransition,BaseTransitionPropsValidators:BaseTransitionPropsValidators,Comment:Comment,DeprecationTypes:DeprecationTypes,EffectScope:EffectScope,ErrorCodes:ErrorCodes,ErrorTypeStrings:ErrorTypeStrings,Fragment:Fragment,KeepAlive:KeepAlive,ReactiveEffect:ReactiveEffect,Static:Static,Suspense:Suspense,Teleport:Teleport,Text:Text,TrackOpTypes:TrackOpTypes,Transition:Transition,TransitionGroup:TransitionGroup,TriggerOpTypes:TriggerOpTypes,VueElement:VueElement,assertNumber:assertNumber,callWithAsyncErrorHandling:callWithAsyncErrorHandling,callWithErrorHandling:callWithErrorHandling,camelize:camelize,capitalize:capitalize,cloneVNode:cloneVNode,compatUtils:compatUtils,computed:computed,createApp:createApp,createBlock:createBlock,createCommentVNode:createCommentVNode,createElementBlock:createElementBlock,createElementVNode:createBaseVNode,createHydrationRenderer:createHydrationRenderer,createPropsRestProxy:createPropsRestProxy,createRenderer:createRenderer,createSSRApp:createSSRApp,createSlots:createSlots,createStaticVNode:createStaticVNode,createTextVNode:createTextVNode,createVNode:createVNode,customRef:customRef,defineAsyncComponent:defineAsyncComponent,defineComponent:defineComponent,defineCustomElement:defineCustomElement,defineEmits:defineEmits,defineExpose:defineExpose,defineModel:defineModel,defineOptions:defineOptions,defineProps:defineProps,defineSSRCustomElement:defineSSRCustomElement,defineSlots:defineSlots,devtools:devtools,effect:effect,effectScope:effectScope,getCurrentInstance:getCurrentInstance,getCurrentScope:getCurrentScope,getCurrentWatcher:getCurrentWatcher,getTransitionRawChildren:getTransitionRawChildren,guardReactiveProps:guardReactiveProps,h:h,handleError:handleError,hasInjectionContext:hasInjectionContext,hydrate:hydrate,hydrateOnIdle:hydrateOnIdle,hydrateOnInteraction:hydrateOnInteraction,hydrateOnMediaQuery:hydrateOnMediaQuery,hydrateOnVisible:hydrateOnVisible,initCustomFormatter:initCustomFormatter,initDirectivesForSSR:initDirectivesForSSR,inject:inject,isMemoSame:isMemoSame,isProxy:isProxy,isReactive:isReactive,isReadonly:isReadonly,isRef:isRef,isRuntimeOnly:isRuntimeOnly,isShallow:isShallow,isVNode:isVNode,markRaw:markRaw,mergeDefaults:mergeDefaults,mergeModels:mergeModels,mergeProps:mergeProps,nextTick:nextTick,normalizeClass:normalizeClass,normalizeProps:normalizeProps,normalizeStyle:normalizeStyle,onActivated:onActivated,onBeforeMount:onBeforeMount,onBeforeUnmount:onBeforeUnmount,onBeforeUpdate:onBeforeUpdate,onDeactivated:onDeactivated,onErrorCaptured:onErrorCaptured,onMounted:onMounted,onRenderTracked:onRenderTracked,onRenderTriggered:onRenderTriggered,onScopeDispose:onScopeDispose,onServerPrefetch:onServerPrefetch,onUnmounted:onUnmounted,onUpdated:onUpdated,onWatcherCleanup:onWatcherCleanup,openBlock:openBlock,popScopeId:popScopeId,provide:provide,proxyRefs:proxyRefs,pushScopeId:pushScopeId,queuePostFlushCb:queuePostFlushCb,reactive:reactive,readonly:readonly,ref:ref,registerRuntimeCompiler:registerRuntimeCompiler,render:render,renderList:renderList,renderSlot:renderSlot,resolveComponent:resolveComponent,resolveDirective:resolveDirective,resolveDynamicComponent:resolveDynamicComponent,resolveFilter:resolveFilter,resolveTransitionHooks:resolveTransitionHooks,setBlockTracking:setBlockTracking,setDevtoolsHook:setDevtoolsHook,setTransitionHooks:setTransitionHooks,shallowReactive:shallowReactive,shallowReadonly:shallowReadonly,shallowRef:shallowRef,ssrContextKey:ssrContextKey,ssrUtils:ssrUtils,stop:stop,toDisplayString:toDisplayString,toHandlerKey:toHandlerKey,toHandlers:toHandlers,toRaw:toRaw,toRef:toRef,toRefs:toRefs,toValue:toValue,transformVNodeArgs:transformVNodeArgs,triggerRef:triggerRef,unref:unref,useAttrs:useAttrs,useCssModule:useCssModule,useCssVars:useCssVars,useHost:useHost,useId:useId,useModel:useModel,useSSRContext:useSSRContext,useShadowRoot:useShadowRoot,useSlots:useSlots,useTemplateRef:useTemplateRef,useTransitionState:useTransitionState,vModelCheckbox:vModelCheckbox,vModelDynamic:vModelDynamic,vModelRadio:vModelRadio,vModelSelect:vModelSelect,vModelText:vModelText,vShow:vShow,version:version,warn:warn,watch:watch,watchEffect:watchEffect,watchPostEffect:watchPostEffect,watchSyncEffect:watchSyncEffect,withAsyncContext:withAsyncContext,withCtx:withCtx,withDefaults:withDefaults,withDirectives:withDirectives,withKeys:withKeys,withMemo:withMemo,withModifiers:withModifiers,withScopeId:withScopeId},Symbol.toStringTag,{value:"Module"})),FRAGMENT=Symbol(""),TELEPORT=Symbol(""),SUSPENSE=Symbol(""),KEEP_ALIVE=Symbol(""),BASE_TRANSITION=Symbol(""),OPEN_BLOCK=Symbol(""),CREATE_BLOCK=Symbol(""),CREATE_ELEMENT_BLOCK=Symbol(""),CREATE_VNODE=Symbol(""),CREATE_ELEMENT_VNODE=Symbol(""),CREATE_COMMENT=Symbol(""),CREATE_TEXT=Symbol(""),CREATE_STATIC=Symbol(""),RESOLVE_COMPONENT=Symbol(""),RESOLVE_DYNAMIC_COMPONENT=Symbol(""),RESOLVE_DIRECTIVE=Symbol(""),RESOLVE_FILTER=Symbol(""),WITH_DIRECTIVES=Symbol(""),RENDER_LIST=Symbol(""),RENDER_SLOT=Symbol(""),CREATE_SLOTS=Symbol(""),TO_DISPLAY_STRING=Symbol(""),MERGE_PROPS=Symbol(""),NORMALIZE_CLASS=Symbol(""),NORMALIZE_STYLE=Symbol(""),NORMALIZE_PROPS=Symbol(""),GUARD_REACTIVE_PROPS=Symbol(""),TO_HANDLERS=Symbol(""),CAMELIZE=Symbol(""),CAPITALIZE=Symbol(""),TO_HANDLER_KEY=Symbol(""),SET_BLOCK_TRACKING=Symbol(""),PUSH_SCOPE_ID=Symbol(""),POP_SCOPE_ID=Symbol(""),WITH_CTX=Symbol(""),UNREF=Symbol(""),IS_REF=Symbol(""),WITH_MEMO=Symbol(""),IS_MEMO_SAME=Symbol(""),helperNameMap={[FRAGMENT]:"Fragment",[TELEPORT]:"Teleport",[SUSPENSE]:"Suspense",[KEEP_ALIVE]:"KeepAlive",[BASE_TRANSITION]:"BaseTransition",[OPEN_BLOCK]:"openBlock",[CREATE_BLOCK]:"createBlock",[CREATE_ELEMENT_BLOCK]:"createElementBlock",[CREATE_VNODE]:"createVNode",[CREATE_ELEMENT_VNODE]:"createElementVNode",[CREATE_COMMENT]:"createCommentVNode",[CREATE_TEXT]:"createTextVNode",[CREATE_STATIC]:"createStaticVNode",[RESOLVE_COMPONENT]:"resolveComponent",[RESOLVE_DYNAMIC_COMPONENT]:"resolveDynamicComponent",[RESOLVE_DIRECTIVE]:"resolveDirective",[RESOLVE_FILTER]:"resolveFilter",[WITH_DIRECTIVES]:"withDirectives",[RENDER_LIST]:"renderList",[RENDER_SLOT]:"renderSlot",[CREATE_SLOTS]:"createSlots",[TO_DISPLAY_STRING]:"toDisplayString",[MERGE_PROPS]:"mergeProps",[NORMALIZE_CLASS]:"normalizeClass",[NORMALIZE_STYLE]:"normalizeStyle",[NORMALIZE_PROPS]:"normalizeProps",[GUARD_REACTIVE_PROPS]:"guardReactiveProps",[TO_HANDLERS]:"toHandlers",[CAMELIZE]:"camelize",[CAPITALIZE]:"capitalize",[TO_HANDLER_KEY]:"toHandlerKey",[SET_BLOCK_TRACKING]:"setBlockTracking",[PUSH_SCOPE_ID]:"pushScopeId",[POP_SCOPE_ID]:"popScopeId",[WITH_CTX]:"withCtx",[UNREF]:"unref",[IS_REF]:"isRef",[WITH_MEMO]:"withMemo",[IS_MEMO_SAME]:"isMemoSame"};function registerRuntimeHelpers(e){Object.getOwnPropertySymbols(e).forEach(t=>{helperNameMap[t]=e[t]})}const locStub={start:{line:1,column:1,offset:0},end:{line:1,column:1,offset:0},source:""};function createRoot(e,t=""){return{type:0,source:t,children:e,helpers:new Set,components:[],directives:[],hoists:[],imports:[],cached:[],temps:0,codegenNode:void 0,loc:locStub}}function createVNodeCall(e,t,n,s,r,o,i,a=!1,c=!1,l=!1,d=locStub){return e&&(a?(e.helper(OPEN_BLOCK),e.helper(getVNodeBlockHelper(e.inSSR,l))):e.helper(getVNodeHelper(e.inSSR,l)),i&&e.helper(WITH_DIRECTIVES)),{type:13,tag:t,props:n,children:s,patchFlag:r,dynamicProps:o,directives:i,isBlock:a,disableTracking:c,isComponent:l,loc:d}}function createArrayExpression(e,t=locStub){return{type:17,loc:t,elements:e}}function createObjectExpression(e,t=locStub){return{type:15,loc:t,properties:e}}function createObjectProperty(e,t){return{type:16,loc:locStub,key:isString(e)?createSimpleExpression(e,!0):e,value:t}}function createSimpleExpression(e,t=!1,n=locStub,s=0){return{type:4,loc:n,content:e,isStatic:t,constType:t?3:s}}function createCompoundExpression(e,t=locStub){return{type:8,loc:t,children:e}}function createCallExpression(e,t=[],n=locStub){return{type:14,loc:n,callee:e,arguments:t}}function createFunctionExpression(e,t=void 0,n=!1,s=!1,r=locStub){return{type:18,params:e,returns:t,newline:n,isSlot:s,loc:r}}function createConditionalExpression(e,t,n,s=!0){return{type:19,test:e,consequent:t,alternate:n,newline:s,loc:locStub}}function createCacheExpression(e,t,n=!1,s=!1){return{type:20,index:e,value:t,needPauseTracking:n,inVOnce:s,needArraySpread:!1,loc:locStub}}function createBlockStatement(e){return{type:21,body:e,loc:locStub}}function getVNodeHelper(e,t){return e||t?CREATE_VNODE:CREATE_ELEMENT_VNODE}function getVNodeBlockHelper(e,t){return e||t?CREATE_BLOCK:CREATE_ELEMENT_BLOCK}function convertToBlock(e,{helper:t,removeHelper:n,inSSR:s}){e.isBlock||(e.isBlock=!0,n(getVNodeHelper(s,e.isComponent)),t(OPEN_BLOCK),t(getVNodeBlockHelper(s,e.isComponent)))}const defaultDelimitersOpen=new Uint8Array([123,123]),defaultDelimitersClose=new Uint8Array([125,125]);function isTagStartChar(e){return e>=97&&e<=122||e>=65&&e<=90}function isWhitespace(e){return 32===e||10===e||9===e||12===e||13===e}function isEndOfTagSection(e){return 47===e||62===e||isWhitespace(e)}function toCharCodes(e){const t=new Uint8Array(e.length);for(let n=0;n<e.length;n++)t[n]=e.charCodeAt(n);return t}const Sequences={Cdata:new Uint8Array([67,68,65,84,65,91]),CdataEnd:new Uint8Array([93,93,62]),CommentEnd:new Uint8Array([45,45,62]),ScriptEnd:new Uint8Array([60,47,115,99,114,105,112,116]),StyleEnd:new Uint8Array([60,47,115,116,121,108,101]),TitleEnd:new Uint8Array([60,47,116,105,116,108,101]),TextareaEnd:new Uint8Array([60,47,116,101,120,116,97,114,101,97])};class Tokenizer{constructor(e,t){this.stack=e,this.cbs=t,this.state=1,this.buffer="",this.sectionStart=0,this.index=0,this.entityStart=0,this.baseState=1,this.inRCDATA=!1,this.inXML=!1,this.inVPre=!1,this.newlines=[],this.mode=0,this.delimiterOpen=defaultDelimitersOpen,this.delimiterClose=defaultDelimitersClose,this.delimiterIndex=-1,this.currentSequence=void 0,this.sequenceIndex=0}get inSFCRoot(){return 2===this.mode&&0===this.stack.length}reset(){this.state=1,this.mode=0,this.buffer="",this.sectionStart=0,this.index=0,this.baseState=1,this.inRCDATA=!1,this.currentSequence=void 0,this.newlines.length=0,this.delimiterOpen=defaultDelimitersOpen,this.delimiterClose=defaultDelimitersClose}getPos(e){let t=1,n=e+1;for(let s=this.newlines.length-1;s>=0;s--){const r=this.newlines[s];if(e>r){t=s+2,n=e-r;break}}return{column:n,line:t,offset:e}}peek(){return this.buffer.charCodeAt(this.index+1)}stateText(e){60===e?(this.index>this.sectionStart&&this.cbs.ontext(this.sectionStart,this.index),this.state=5,this.sectionStart=this.index):this.inVPre||e!==this.delimiterOpen[0]||(this.state=2,this.delimiterIndex=0,this.stateInterpolationOpen(e))}stateInterpolationOpen(e){if(e===this.delimiterOpen[this.delimiterIndex])if(this.delimiterIndex===this.delimiterOpen.length-1){const e=this.index+1-this.delimiterOpen.length;e>this.sectionStart&&this.cbs.ontext(this.sectionStart,e),this.state=3,this.sectionStart=e}else this.delimiterIndex++;else this.inRCDATA?(this.state=32,this.stateInRCDATA(e)):(this.state=1,this.stateText(e))}stateInterpolation(e){e===this.delimiterClose[0]&&(this.state=4,this.delimiterIndex=0,this.stateInterpolationClose(e))}stateInterpolationClose(e){e===this.delimiterClose[this.delimiterIndex]?this.delimiterIndex===this.delimiterClose.length-1?(this.cbs.oninterpolation(this.sectionStart,this.index+1),this.inRCDATA?this.state=32:this.state=1,this.sectionStart=this.index+1):this.delimiterIndex++:(this.state=3,this.stateInterpolation(e))}stateSpecialStartSequence(e){const t=this.sequenceIndex===this.currentSequence.length;if(t?isEndOfTagSection(e):(32|e)===this.currentSequence[this.sequenceIndex]){if(!t)return void this.sequenceIndex++}else this.inRCDATA=!1;this.sequenceIndex=0,this.state=6,this.stateInTagName(e)}stateInRCDATA(e){if(this.sequenceIndex===this.currentSequence.length){if(62===e||isWhitespace(e)){const t=this.index-this.currentSequence.length;if(this.sectionStart<t){const e=this.index;this.index=t,this.cbs.ontext(this.sectionStart,t),this.index=e}return this.sectionStart=t+2,this.stateInClosingTagName(e),void(this.inRCDATA=!1)}this.sequenceIndex=0}(32|e)===this.currentSequence[this.sequenceIndex]?this.sequenceIndex+=1:0===this.sequenceIndex?this.currentSequence===Sequences.TitleEnd||this.currentSequence===Sequences.TextareaEnd&&!this.inSFCRoot?this.inVPre||e!==this.delimiterOpen[0]||(this.state=2,this.delimiterIndex=0,this.stateInterpolationOpen(e)):this.fastForwardTo(60)&&(this.sequenceIndex=1):this.sequenceIndex=Number(60===e)}stateCDATASequence(e){e===Sequences.Cdata[this.sequenceIndex]?++this.sequenceIndex===Sequences.Cdata.length&&(this.state=28,this.currentSequence=Sequences.CdataEnd,this.sequenceIndex=0,this.sectionStart=this.index+1):(this.sequenceIndex=0,this.state=23,this.stateInDeclaration(e))}fastForwardTo(e){for(;++this.index<this.buffer.length;){const t=this.buffer.charCodeAt(this.index);if(10===t&&this.newlines.push(this.index),t===e)return!0}return this.index=this.buffer.length-1,!1}stateInCommentLike(e){e===this.currentSequence[this.sequenceIndex]?++this.sequenceIndex===this.currentSequence.length&&(this.currentSequence===Sequences.CdataEnd?this.cbs.oncdata(this.sectionStart,this.index-2):this.cbs.oncomment(this.sectionStart,this.index-2),this.sequenceIndex=0,this.sectionStart=this.index+1,this.state=1):0===this.sequenceIndex?this.fastForwardTo(this.currentSequence[0])&&(this.sequenceIndex=1):e!==this.currentSequence[this.sequenceIndex-1]&&(this.sequenceIndex=0)}startSpecial(e,t){this.enterRCDATA(e,t),this.state=31}enterRCDATA(e,t){this.inRCDATA=!0,this.currentSequence=e,this.sequenceIndex=t}stateBeforeTagName(e){33===e?(this.state=22,this.sectionStart=this.index+1):63===e?(this.state=24,this.sectionStart=this.index+1):isTagStartChar(e)?(this.sectionStart=this.index,0===this.mode?this.state=6:this.inSFCRoot?this.state=34:this.inXML?this.state=6:this.state=116===e?30:115===e?29:6):47===e?this.state=8:(this.state=1,this.stateText(e))}stateInTagName(e){isEndOfTagSection(e)&&this.handleTagName(e)}stateInSFCRootTagName(e){if(isEndOfTagSection(e)){const t=this.buffer.slice(this.sectionStart,this.index);"template"!==t&&this.enterRCDATA(toCharCodes("</"+t),0),this.handleTagName(e)}}handleTagName(e){this.cbs.onopentagname(this.sectionStart,this.index),this.sectionStart=-1,this.state=11,this.stateBeforeAttrName(e)}stateBeforeClosingTagName(e){isWhitespace(e)||(62===e?(this.state=1,this.sectionStart=this.index+1):(this.state=isTagStartChar(e)?9:27,this.sectionStart=this.index))}stateInClosingTagName(e){(62===e||isWhitespace(e))&&(this.cbs.onclosetag(this.sectionStart,this.index),this.sectionStart=-1,this.state=10,this.stateAfterClosingTagName(e))}stateAfterClosingTagName(e){62===e&&(this.state=1,this.sectionStart=this.index+1)}stateBeforeAttrName(e){62===e?(this.cbs.onopentagend(this.index),this.inRCDATA?this.state=32:this.state=1,this.sectionStart=this.index+1):47===e?this.state=7:60===e&&47===this.peek()?(this.cbs.onopentagend(this.index),this.state=5,this.sectionStart=this.index):isWhitespace(e)||this.handleAttrStart(e)}handleAttrStart(e){118===e&&45===this.peek()?(this.state=13,this.sectionStart=this.index):46===e||58===e||64===e||35===e?(this.cbs.ondirname(this.index,this.index+1),this.state=14,this.sectionStart=this.index+1):(this.state=12,this.sectionStart=this.index)}stateInSelfClosingTag(e){62===e?(this.cbs.onselfclosingtag(this.index),this.state=1,this.sectionStart=this.index+1,this.inRCDATA=!1):isWhitespace(e)||(this.state=11,this.stateBeforeAttrName(e))}stateInAttrName(e){(61===e||isEndOfTagSection(e))&&(this.cbs.onattribname(this.sectionStart,this.index),this.handleAttrNameEnd(e))}stateInDirName(e){61===e||isEndOfTagSection(e)?(this.cbs.ondirname(this.sectionStart,this.index),this.handleAttrNameEnd(e)):58===e?(this.cbs.ondirname(this.sectionStart,this.index),this.state=14,this.sectionStart=this.index+1):46===e&&(this.cbs.ondirname(this.sectionStart,this.index),this.state=16,this.sectionStart=this.index+1)}stateInDirArg(e){61===e||isEndOfTagSection(e)?(this.cbs.ondirarg(this.sectionStart,this.index),this.handleAttrNameEnd(e)):91===e?this.state=15:46===e&&(this.cbs.ondirarg(this.sectionStart,this.index),this.state=16,this.sectionStart=this.index+1)}stateInDynamicDirArg(e){93===e?this.state=14:(61===e||isEndOfTagSection(e))&&(this.cbs.ondirarg(this.sectionStart,this.index+1),this.handleAttrNameEnd(e))}stateInDirModifier(e){61===e||isEndOfTagSection(e)?(this.cbs.ondirmodifier(this.sectionStart,this.index),this.handleAttrNameEnd(e)):46===e&&(this.cbs.ondirmodifier(this.sectionStart,this.index),this.sectionStart=this.index+1)}handleAttrNameEnd(e){this.sectionStart=this.index,this.state=17,this.cbs.onattribnameend(this.index),this.stateAfterAttrName(e)}stateAfterAttrName(e){61===e?this.state=18:47===e||62===e?(this.cbs.onattribend(0,this.sectionStart),this.sectionStart=-1,this.state=11,this.stateBeforeAttrName(e)):isWhitespace(e)||(this.cbs.onattribend(0,this.sectionStart),this.handleAttrStart(e))}stateBeforeAttrValue(e){34===e?(this.state=19,this.sectionStart=this.index+1):39===e?(this.state=20,this.sectionStart=this.index+1):isWhitespace(e)||(this.sectionStart=this.index,this.state=21,this.stateInAttrValueNoQuotes(e))}handleInAttrValue(e,t){(e===t||this.fastForwardTo(t))&&(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(34===t?3:2,this.index+1),this.state=11)}stateInAttrValueDoubleQuotes(e){this.handleInAttrValue(e,34)}stateInAttrValueSingleQuotes(e){this.handleInAttrValue(e,39)}stateInAttrValueNoQuotes(e){isWhitespace(e)||62===e?(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(1,this.index),this.state=11,this.stateBeforeAttrName(e)):39!==e&&60!==e&&61!==e&&96!==e||this.cbs.onerr(18,this.index)}stateBeforeDeclaration(e){91===e?(this.state=26,this.sequenceIndex=0):this.state=45===e?25:23}stateInDeclaration(e){(62===e||this.fastForwardTo(62))&&(this.state=1,this.sectionStart=this.index+1)}stateInProcessingInstruction(e){(62===e||this.fastForwardTo(62))&&(this.cbs.onprocessinginstruction(this.sectionStart,this.index),this.state=1,this.sectionStart=this.index+1)}stateBeforeComment(e){45===e?(this.state=28,this.currentSequence=Sequences.CommentEnd,this.sequenceIndex=2,this.sectionStart=this.index+1):this.state=23}stateInSpecialComment(e){(62===e||this.fastForwardTo(62))&&(this.cbs.oncomment(this.sectionStart,this.index),this.state=1,this.sectionStart=this.index+1)}stateBeforeSpecialS(e){e===Sequences.ScriptEnd[3]?this.startSpecial(Sequences.ScriptEnd,4):e===Sequences.StyleEnd[3]?this.startSpecial(Sequences.StyleEnd,4):(this.state=6,this.stateInTagName(e))}stateBeforeSpecialT(e){e===Sequences.TitleEnd[3]?this.startSpecial(Sequences.TitleEnd,4):e===Sequences.TextareaEnd[3]?this.startSpecial(Sequences.TextareaEnd,4):(this.state=6,this.stateInTagName(e))}startEntity(){}stateInEntity(){}parse(e){for(this.buffer=e;this.index<this.buffer.length;){const e=this.buffer.charCodeAt(this.index);switch(10===e&&33!==this.state&&this.newlines.push(this.index),this.state){case 1:this.stateText(e);break;case 2:this.stateInterpolationOpen(e);break;case 3:this.stateInterpolation(e);break;case 4:this.stateInterpolationClose(e);break;case 31:this.stateSpecialStartSequence(e);break;case 32:this.stateInRCDATA(e);break;case 26:this.stateCDATASequence(e);break;case 19:this.stateInAttrValueDoubleQuotes(e);break;case 12:this.stateInAttrName(e);break;case 13:this.stateInDirName(e);break;case 14:this.stateInDirArg(e);break;case 15:this.stateInDynamicDirArg(e);break;case 16:this.stateInDirModifier(e);break;case 28:this.stateInCommentLike(e);break;case 27:this.stateInSpecialComment(e);break;case 11:this.stateBeforeAttrName(e);break;case 6:this.stateInTagName(e);break;case 34:this.stateInSFCRootTagName(e);break;case 9:this.stateInClosingTagName(e);break;case 5:this.stateBeforeTagName(e);break;case 17:this.stateAfterAttrName(e);break;case 20:this.stateInAttrValueSingleQuotes(e);break;case 18:this.stateBeforeAttrValue(e);break;case 8:this.stateBeforeClosingTagName(e);break;case 10:this.stateAfterClosingTagName(e);break;case 29:this.stateBeforeSpecialS(e);break;case 30:this.stateBeforeSpecialT(e);break;case 21:this.stateInAttrValueNoQuotes(e);break;case 7:this.stateInSelfClosingTag(e);break;case 23:this.stateInDeclaration(e);break;case 22:this.stateBeforeDeclaration(e);break;case 25:this.stateBeforeComment(e);break;case 24:this.stateInProcessingInstruction(e);break;case 33:this.stateInEntity()}this.index++}this.cleanup(),this.finish()}cleanup(){this.sectionStart!==this.index&&(1===this.state||32===this.state&&0===this.sequenceIndex?(this.cbs.ontext(this.sectionStart,this.index),this.sectionStart=this.index):19!==this.state&&20!==this.state&&21!==this.state||(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=this.index))}finish(){this.handleTrailingData(),this.cbs.onend()}handleTrailingData(){const e=this.buffer.length;this.sectionStart>=e||(28===this.state?this.currentSequence===Sequences.CdataEnd?this.cbs.oncdata(this.sectionStart,e):this.cbs.oncomment(this.sectionStart,e):6===this.state||11===this.state||18===this.state||17===this.state||12===this.state||13===this.state||14===this.state||15===this.state||16===this.state||20===this.state||19===this.state||21===this.state||9===this.state||this.cbs.ontext(this.sectionStart,e))}emitCodePoint(e,t){}}function getCompatValue(e,{compatConfig:t}){const n=t&&t[e];return"MODE"===e?n||3:n}function isCompatEnabled(e,t){const n=getCompatValue("MODE",t),s=getCompatValue(e,t);return 3===n?!0===s:!1!==s}function checkCompatEnabled(e,t,n,...s){return isCompatEnabled(e,t)}function defaultOnError(e){throw e}function defaultOnWarn(e){}function createCompilerError(e,t,n,s){const r=new SyntaxError(String(`https://vuejs.org/error-reference/#compiler-${e}`));return r.code=e,r.loc=t,r}const isStaticExp=e=>4===e.type&&e.isStatic;function isCoreComponent(e){switch(e){case"Teleport":case"teleport":return TELEPORT;case"Suspense":case"suspense":return SUSPENSE;case"KeepAlive":case"keep-alive":return KEEP_ALIVE;case"BaseTransition":case"base-transition":return BASE_TRANSITION}}const nonIdentifierRE=/^$|^\d|[^\$\w\xA0-\uFFFF]/,isSimpleIdentifier=e=>!nonIdentifierRE.test(e),validFirstIdentCharRE=/[A-Za-z_$\xA0-\uFFFF]/,validIdentCharRE=/[\.\?\w$\xA0-\uFFFF]/,whitespaceRE=/\s+[.[]\s*|\s*[.[]\s+/g,getExpSource=e=>4===e.type?e.content:e.loc.source,isMemberExpressionBrowser=e=>{const t=getExpSource(e).trim().replace(whitespaceRE,e=>e.trim());let n=0,s=[],r=0,o=0,i=null;for(let a=0;a<t.length;a++){const e=t.charAt(a);switch(n){case 0:if("["===e)s.push(n),n=1,r++;else if("("===e)s.push(n),n=2,o++;else if(!(0===a?validFirstIdentCharRE:validIdentCharRE).test(e))return!1;break;case 1:"'"===e||'"'===e||"`"===e?(s.push(n),n=3,i=e):"["===e?r++:"]"===e&&(--r||(n=s.pop()));break;case 2:if("'"===e||'"'===e||"`"===e)s.push(n),n=3,i=e;else if("("===e)o++;else if(")"===e){if(a===t.length-1)return!1;--o||(n=s.pop())}break;case 3:e===i&&(n=s.pop(),i=null)}}return!r&&!o},isMemberExpression=isMemberExpressionBrowser,fnExpRE=/^\s*(async\s*)?(\([^)]*?\)|[\w$_]+)\s*(:[^=]+)?=>|^\s*(async\s+)?function(?:\s+[\w$]+)?\s*\(/,isFnExpressionBrowser=e=>fnExpRE.test(getExpSource(e)),isFnExpression=isFnExpressionBrowser;function findDir(e,t,n=!1){for(let s=0;s<e.props.length;s++){const r=e.props[s];if(7===r.type&&(n||r.exp)&&(isString(t)?r.name===t:t.test(r.name)))return r}}function findProp(e,t,n=!1,s=!1){for(let r=0;r<e.props.length;r++){const o=e.props[r];if(6===o.type){if(n)continue;if(o.name===t&&(o.value||s))return o}else if("bind"===o.name&&(o.exp||s)&&isStaticArgOf(o.arg,t))return o}}function isStaticArgOf(e,t){return!(!e||!isStaticExp(e)||e.content!==t)}function hasDynamicKeyVBind(e){return e.props.some(e=>!(7!==e.type||"bind"!==e.name||e.arg&&4===e.arg.type&&e.arg.isStatic))}function isText$1(e){return 5===e.type||2===e.type}function isVPre(e){return 7===e.type&&"pre"===e.name}function isVSlot(e){return 7===e.type&&"slot"===e.name}function isTemplateNode(e){return 1===e.type&&3===e.tagType}function isSlotOutlet(e){return 1===e.type&&2===e.tagType}const propsHelperSet=new Set([NORMALIZE_PROPS,GUARD_REACTIVE_PROPS]);function getUnnormalizedProps(e,t=[]){if(e&&!isString(e)&&14===e.type){const n=e.callee;if(!isString(n)&&propsHelperSet.has(n))return getUnnormalizedProps(e.arguments[0],t.concat(e))}return[e,t]}function injectProp(e,t,n){let s,r,o=13===e.type?e.props:e.arguments[2],i=[];if(o&&!isString(o)&&14===o.type){const e=getUnnormalizedProps(o);o=e[0],i=e[1],r=i[i.length-1]}if(null==o||isString(o))s=createObjectExpression([t]);else if(14===o.type){const e=o.arguments[0];isString(e)||15!==e.type?o.callee===TO_HANDLERS?s=createCallExpression(n.helper(MERGE_PROPS),[createObjectExpression([t]),o]):o.arguments.unshift(createObjectExpression([t])):hasProp(t,e)||e.properties.unshift(t),!s&&(s=o)}else 15===o.type?(hasProp(t,o)||o.properties.unshift(t),s=o):(s=createCallExpression(n.helper(MERGE_PROPS),[createObjectExpression([t]),o]),r&&r.callee===GUARD_REACTIVE_PROPS&&(r=i[i.length-2]));13===e.type?r?r.arguments[0]=s:e.props=s:r?r.arguments[0]=s:e.arguments[2]=s}function hasProp(e,t){let n=!1;if(4===e.key.type){const s=e.key.content;n=t.properties.some(e=>4===e.key.type&&e.key.content===s)}return n}function toValidAssetId(e,t){return`_${t}_${e.replace(/[^\w]/g,(t,n)=>"-"===t?"_":e.charCodeAt(n).toString())}`}function getMemoedVNodeCall(e){return 14===e.type&&e.callee===WITH_MEMO?e.arguments[1].returns:e}const forAliasRE=/([\s\S]*?)\s+(?:in|of)\s+(\S[\s\S]*)/,defaultParserOptions={parseMode:"base",ns:0,delimiters:["{{","}}"],getNamespace:()=>0,isVoidTag:NO,isPreTag:NO,isIgnoreNewlineTag:NO,isCustomElement:NO,onError:defaultOnError,onWarn:defaultOnWarn,comments:!1,prefixIdentifiers:!1};let currentOptions=defaultParserOptions,currentRoot=null,currentInput="",currentOpenTag=null,currentProp=null,currentAttrValue="",currentAttrStartIndex=-1,currentAttrEndIndex=-1,inPre=0,inVPre=!1,currentVPreBoundary=null;const stack=[],tokenizer=new Tokenizer(stack,{onerr:emitError,ontext(e,t){onText(getSlice(e,t),e,t)},ontextentity(e,t,n){onText(e,t,n)},oninterpolation(e,t){if(inVPre)return onText(getSlice(e,t),e,t);let n=e+tokenizer.delimiterOpen.length,s=t-tokenizer.delimiterClose.length;for(;isWhitespace(currentInput.charCodeAt(n));)n++;for(;isWhitespace(currentInput.charCodeAt(s-1));)s--;let r=getSlice(n,s);r.includes("&")&&(r=currentOptions.decodeEntities(r,!1)),addNode({type:5,content:createExp(r,!1,getLoc(n,s)),loc:getLoc(e,t)})},onopentagname(e,t){const n=getSlice(e,t);currentOpenTag={type:1,tag:n,ns:currentOptions.getNamespace(n,stack[0],currentOptions.ns),tagType:0,props:[],children:[],loc:getLoc(e-1,t),codegenNode:void 0}},onopentagend(e){endOpenTag(e)},onclosetag(e,t){const n=getSlice(e,t);if(!currentOptions.isVoidTag(n)){let s=!1;for(let e=0;e<stack.length;e++){if(stack[e].tag.toLowerCase()===n.toLowerCase()){s=!0,e>0&&emitError(24,stack[0].loc.start.offset);for(let n=0;n<=e;n++){onCloseTag(stack.shift(),t,n<e)}break}}s||emitError(23,backTrack(e,60))}},onselfclosingtag(e){const t=currentOpenTag.tag;currentOpenTag.isSelfClosing=!0,endOpenTag(e),stack[0]&&stack[0].tag===t&&onCloseTag(stack.shift(),e)},onattribname(e,t){currentProp={type:6,name:getSlice(e,t),nameLoc:getLoc(e,t),value:void 0,loc:getLoc(e)}},ondirname(e,t){const n=getSlice(e,t),s="."===n||":"===n?"bind":"@"===n?"on":"#"===n?"slot":n.slice(2);if(inVPre||""!==s||emitError(26,e),inVPre||""===s)currentProp={type:6,name:n,nameLoc:getLoc(e,t),value:void 0,loc:getLoc(e)};else if(currentProp={type:7,name:s,rawName:n,exp:void 0,arg:void 0,modifiers:"."===n?[createSimpleExpression("prop")]:[],loc:getLoc(e)},"pre"===s){inVPre=tokenizer.inVPre=!0,currentVPreBoundary=currentOpenTag;const e=currentOpenTag.props;for(let t=0;t<e.length;t++)7===e[t].type&&(e[t]=dirToAttr(e[t]))}},ondirarg(e,t){if(e===t)return;const n=getSlice(e,t);if(inVPre&&!isVPre(currentProp))currentProp.name+=n,setLocEnd(currentProp.nameLoc,t);else{const s="["!==n[0];currentProp.arg=createExp(s?n:n.slice(1,-1),s,getLoc(e,t),s?3:0)}},ondirmodifier(e,t){const n=getSlice(e,t);if(inVPre&&!isVPre(currentProp))currentProp.name+="."+n,setLocEnd(currentProp.nameLoc,t);else if("slot"===currentProp.name){const e=currentProp.arg;e&&(e.content+="."+n,setLocEnd(e.loc,t))}else{const s=createSimpleExpression(n,!0,getLoc(e,t));currentProp.modifiers.push(s)}},onattribdata(e,t){currentAttrValue+=getSlice(e,t),currentAttrStartIndex<0&&(currentAttrStartIndex=e),currentAttrEndIndex=t},onattribentity(e,t,n){currentAttrValue+=e,currentAttrStartIndex<0&&(currentAttrStartIndex=t),currentAttrEndIndex=n},onattribnameend(e){const t=currentProp.loc.start.offset,n=getSlice(t,e);7===currentProp.type&&(currentProp.rawName=n),currentOpenTag.props.some(e=>(7===e.type?e.rawName:e.name)===n)&&emitError(2,t)},onattribend(e,t){if(currentOpenTag&&currentProp){if(setLocEnd(currentProp.loc,t),0!==e)if(currentAttrValue.includes("&")&&(currentAttrValue=currentOptions.decodeEntities(currentAttrValue,!0)),6===currentProp.type)"class"===currentProp.name&&(currentAttrValue=condense(currentAttrValue).trim()),1!==e||currentAttrValue||emitError(13,t),currentProp.value={type:2,content:currentAttrValue,loc:1===e?getLoc(currentAttrStartIndex,currentAttrEndIndex):getLoc(currentAttrStartIndex-1,currentAttrEndIndex+1)},tokenizer.inSFCRoot&&"template"===currentOpenTag.tag&&"lang"===currentProp.name&&currentAttrValue&&"html"!==currentAttrValue&&tokenizer.enterRCDATA(toCharCodes("</template"),0);else{let e=0;currentProp.exp=createExp(currentAttrValue,!1,getLoc(currentAttrStartIndex,currentAttrEndIndex),0,e),"for"===currentProp.name&&(currentProp.forParseResult=parseForExpression(currentProp.exp));let t=-1;"bind"===currentProp.name&&(t=currentProp.modifiers.findIndex(e=>"sync"===e.content))>-1&&checkCompatEnabled("COMPILER_V_BIND_SYNC",currentOptions,currentProp.loc,currentProp.arg.loc.source)&&(currentProp.name="model",currentProp.modifiers.splice(t,1))}7===currentProp.type&&"pre"===currentProp.name||currentOpenTag.props.push(currentProp)}currentAttrValue="",currentAttrStartIndex=currentAttrEndIndex=-1},oncomment(e,t){currentOptions.comments&&addNode({type:3,content:getSlice(e,t),loc:getLoc(e-4,t+3)})},onend(){const e=currentInput.length;for(let t=0;t<stack.length;t++)onCloseTag(stack[t],e-1),emitError(24,stack[t].loc.start.offset)},oncdata(e,t){0!==stack[0].ns?onText(getSlice(e,t),e,t):emitError(1,e-9)},onprocessinginstruction(e){0===(stack[0]?stack[0].ns:currentOptions.ns)&&emitError(21,e-1)}}),forIteratorRE=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/,stripParensRE=/^\(|\)$/g;function parseForExpression(e){const t=e.loc,n=e.content,s=n.match(forAliasRE);if(!s)return;const[,r,o]=s,i=(e,n,s=!1)=>{const r=t.start.offset+n;return createExp(e,!1,getLoc(r,r+e.length),0,s?1:0)},a={source:i(o.trim(),n.indexOf(o,r.length)),value:void 0,key:void 0,index:void 0,finalized:!1};let c=r.trim().replace(stripParensRE,"").trim();const l=r.indexOf(c),d=c.match(forIteratorRE);if(d){c=c.replace(forIteratorRE,"").trim();const e=d[1].trim();let t;if(e&&(t=n.indexOf(e,l+c.length),a.key=i(e,t,!0)),d[2]){const s=d[2].trim();s&&(a.index=i(s,n.indexOf(s,a.key?t+e.length:l+c.length),!0))}}return c&&(a.value=i(c,l,!0)),a}function getSlice(e,t){return currentInput.slice(e,t)}function endOpenTag(e){tokenizer.inSFCRoot&&(currentOpenTag.innerLoc=getLoc(e+1,e+1)),addNode(currentOpenTag);const{tag:t,ns:n}=currentOpenTag;0===n&&currentOptions.isPreTag(t)&&inPre++,currentOptions.isVoidTag(t)?onCloseTag(currentOpenTag,e):(stack.unshift(currentOpenTag),1!==n&&2!==n||(tokenizer.inXML=!0)),currentOpenTag=null}function onText(e,t,n){{const t=stack[0]&&stack[0].tag;"script"!==t&&"style"!==t&&e.includes("&")&&(e=currentOptions.decodeEntities(e,!1))}const s=stack[0]||currentRoot,r=s.children[s.children.length-1];r&&2===r.type?(r.content+=e,setLocEnd(r.loc,n)):s.children.push({type:2,content:e,loc:getLoc(t,n)})}function onCloseTag(e,t,n=!1){setLocEnd(e.loc,n?backTrack(t,60):lookAhead(t,62)+1),tokenizer.inSFCRoot&&(e.children.length?e.innerLoc.end=extend({},e.children[e.children.length-1].loc.end):e.innerLoc.end=extend({},e.innerLoc.start),e.innerLoc.source=getSlice(e.innerLoc.start.offset,e.innerLoc.end.offset));const{tag:s,ns:r,children:o}=e;if(inVPre||("slot"===s?e.tagType=2:isFragmentTemplate(e)?e.tagType=3:isComponent(e)&&(e.tagType=1)),tokenizer.inRCDATA||(e.children=condenseWhitespace(o)),0===r&&currentOptions.isIgnoreNewlineTag(s)){const e=o[0];e&&2===e.type&&(e.content=e.content.replace(/^\r?\n/,""))}0===r&&currentOptions.isPreTag(s)&&inPre--,currentVPreBoundary===e&&(inVPre=tokenizer.inVPre=!1,currentVPreBoundary=null),tokenizer.inXML&&0===(stack[0]?stack[0].ns:currentOptions.ns)&&(tokenizer.inXML=!1);{const t=e.props;if(!tokenizer.inSFCRoot&&isCompatEnabled("COMPILER_NATIVE_TEMPLATE",currentOptions)&&"template"===e.tag&&!isFragmentTemplate(e)){const t=stack[0]||currentRoot,n=t.children.indexOf(e);t.children.splice(n,1,...e.children)}const n=t.find(e=>6===e.type&&"inline-template"===e.name);n&&checkCompatEnabled("COMPILER_INLINE_TEMPLATE",currentOptions,n.loc)&&e.children.length&&(n.value={type:2,content:getSlice(e.children[0].loc.start.offset,e.children[e.children.length-1].loc.end.offset),loc:n.loc})}}function lookAhead(e,t){let n=e;for(;currentInput.charCodeAt(n)!==t&&n<currentInput.length-1;)n++;return n}function backTrack(e,t){let n=e;for(;currentInput.charCodeAt(n)!==t&&n>=0;)n--;return n}const specialTemplateDir=new Set(["if","else","else-if","for","slot"]);function isFragmentTemplate({tag:e,props:t}){if("template"===e)for(let n=0;n<t.length;n++)if(7===t[n].type&&specialTemplateDir.has(t[n].name))return!0;return!1}function isComponent({tag:e,props:t}){if(currentOptions.isCustomElement(e))return!1;if("component"===e||isUpperCase(e.charCodeAt(0))||isCoreComponent(e)||currentOptions.isBuiltInComponent&&currentOptions.isBuiltInComponent(e)||currentOptions.isNativeTag&&!currentOptions.isNativeTag(e))return!0;for(let n=0;n<t.length;n++){const e=t[n];if(6===e.type){if("is"===e.name&&e.value){if(e.value.content.startsWith("vue:"))return!0;if(checkCompatEnabled("COMPILER_IS_ON_ELEMENT",currentOptions,e.loc))return!0}}else if("bind"===e.name&&isStaticArgOf(e.arg,"is")&&checkCompatEnabled("COMPILER_IS_ON_ELEMENT",currentOptions,e.loc))return!0}return!1}function isUpperCase(e){return e>64&&e<91}const windowsNewlineRE=/\r\n/g;function condenseWhitespace(e){const t="preserve"!==currentOptions.whitespace;let n=!1;for(let s=0;s<e.length;s++){const r=e[s];if(2===r.type)if(inPre)r.content=r.content.replace(windowsNewlineRE,"\n");else if(isAllWhitespace(r.content)){const o=e[s-1]&&e[s-1].type,i=e[s+1]&&e[s+1].type;!o||!i||t&&(3===o&&(3===i||1===i)||1===o&&(3===i||1===i&&hasNewlineChar(r.content)))?(n=!0,e[s]=null):r.content=" "}else t&&(r.content=condense(r.content))}return n?e.filter(Boolean):e}function isAllWhitespace(e){for(let t=0;t<e.length;t++)if(!isWhitespace(e.charCodeAt(t)))return!1;return!0}function hasNewlineChar(e){for(let t=0;t<e.length;t++){const n=e.charCodeAt(t);if(10===n||13===n)return!0}return!1}function condense(e){let t="",n=!1;for(let s=0;s<e.length;s++)isWhitespace(e.charCodeAt(s))?n||(t+=" ",n=!0):(t+=e[s],n=!1);return t}function addNode(e){(stack[0]||currentRoot).children.push(e)}function getLoc(e,t){return{start:tokenizer.getPos(e),end:null==t?t:tokenizer.getPos(t),source:null==t?t:getSlice(e,t)}}function cloneLoc(e){return getLoc(e.start.offset,e.end.offset)}function setLocEnd(e,t){e.end=tokenizer.getPos(t),e.source=getSlice(e.start.offset,t)}function dirToAttr(e){const t={type:6,name:e.rawName,nameLoc:getLoc(e.loc.start.offset,e.loc.start.offset+e.rawName.length),value:void 0,loc:e.loc};if(e.exp){const n=e.exp.loc;n.end.offset<e.loc.end.offset&&(n.start.offset--,n.start.column--,n.end.offset++,n.end.column++),t.value={type:2,content:e.exp.content,loc:n}}return t}function createExp(e,t=!1,n,s=0,r=0){return createSimpleExpression(e,t,n,s)}function emitError(e,t,n){currentOptions.onError(createCompilerError(e,getLoc(t,t)))}function reset(){tokenizer.reset(),currentOpenTag=null,currentProp=null,currentAttrValue="",currentAttrStartIndex=-1,currentAttrEndIndex=-1,stack.length=0}function baseParse(e,t){if(reset(),currentInput=e,currentOptions=extend({},defaultParserOptions),t){let e;for(e in t)null!=t[e]&&(currentOptions[e]=t[e])}tokenizer.mode="html"===currentOptions.parseMode?1:"sfc"===currentOptions.parseMode?2:0,tokenizer.inXML=1===currentOptions.ns||2===currentOptions.ns;const n=t&&t.delimiters;n&&(tokenizer.delimiterOpen=toCharCodes(n[0]),tokenizer.delimiterClose=toCharCodes(n[1]));const s=currentRoot=createRoot([],e);return tokenizer.parse(currentInput),s.loc=getLoc(0,e.length),s.children=condenseWhitespace(s.children),currentRoot=null,s}function cacheStatic(e,t){walk(e,void 0,t,!!getSingleElementRoot(e))}function getSingleElementRoot(e){const t=e.children.filter(e=>3!==e.type);return 1!==t.length||1!==t[0].type||isSlotOutlet(t[0])?null:t[0]}function walk(e,t,n,s=!1,r=!1){const{children:o}=e,i=[];for(let u=0;u<o.length;u++){const t=o[u];if(1===t.type&&0===t.tagType){const e=s?0:getConstantType(t,n);if(e>0){if(e>=2){t.codegenNode.patchFlag=-1,i.push(t);continue}}else{const e=t.codegenNode;if(13===e.type){const s=e.patchFlag;if((void 0===s||512===s||1===s)&&getGeneratedPropsConstantType(t,n)>=2){const s=getNodeProps(t);s&&(e.props=n.hoist(s))}e.dynamicProps&&(e.dynamicProps=n.hoist(e.dynamicProps))}}}else if(12===t.type){if((s?0:getConstantType(t,n))>=2){14===t.codegenNode.type&&t.codegenNode.arguments.length>0&&t.codegenNode.arguments.push("-1"),i.push(t);continue}}if(1===t.type){const s=1===t.tagType;s&&n.scopes.vSlot++,walk(t,e,n,!1,r),s&&n.scopes.vSlot--}else if(11===t.type)walk(t,e,n,1===t.children.length,!0);else if(9===t.type)for(let s=0;s<t.branches.length;s++)walk(t.branches[s],e,n,1===t.branches[s].children.length,r)}let a=!1;const c=[];if(i.length===o.length&&1===e.type)if(0===e.tagType&&e.codegenNode&&13===e.codegenNode.type&&isArray(e.codegenNode.children))e.codegenNode.children=l(createArrayExpression(e.codegenNode.children)),a=!0;else if(1===e.tagType&&e.codegenNode&&13===e.codegenNode.type&&e.codegenNode.children&&!isArray(e.codegenNode.children)&&15===e.codegenNode.children.type){const t=d(e.codegenNode,"default");t&&(c.push(n.cached.length),t.returns=l(createArrayExpression(t.returns)),a=!0)}else if(3===e.tagType&&t&&1===t.type&&1===t.tagType&&t.codegenNode&&13===t.codegenNode.type&&t.codegenNode.children&&!isArray(t.codegenNode.children)&&15===t.codegenNode.children.type){const s=findDir(e,"slot",!0),r=s&&s.arg&&d(t.codegenNode,s.arg);r&&(c.push(n.cached.length),r.returns=l(createArrayExpression(r.returns)),a=!0)}if(!a)for(const u of i)c.push(n.cached.length),u.codegenNode=n.cache(u.codegenNode);function l(e){const t=n.cache(e);return r&&n.hmr&&(t.needArraySpread=!0),t}function d(e,t){if(e.children&&!isArray(e.children)&&15===e.children.type){const n=e.children.properties.find(e=>e.key===t||e.key.content===t);return n&&n.value}}c.length&&1===e.type&&1===e.tagType&&e.codegenNode&&13===e.codegenNode.type&&e.codegenNode.children&&!isArray(e.codegenNode.children)&&15===e.codegenNode.children.type&&e.codegenNode.children.properties.push(createObjectProperty("__",createSimpleExpression(JSON.stringify(c),!1))),i.length&&n.transformHoist&&n.transformHoist(o,n,e)}function getConstantType(e,t){const{constantCache:n}=t;switch(e.type){case 1:if(0!==e.tagType)return 0;const s=n.get(e);if(void 0!==s)return s;const r=e.codegenNode;if(13!==r.type)return 0;if(r.isBlock&&"svg"!==e.tag&&"foreignObject"!==e.tag&&"math"!==e.tag)return 0;if(void 0===r.patchFlag){let s=3;const o=getGeneratedPropsConstantType(e,t);if(0===o)return n.set(e,0),0;o<s&&(s=o);for(let r=0;r<e.children.length;r++){const o=getConstantType(e.children[r],t);if(0===o)return n.set(e,0),0;o<s&&(s=o)}if(s>1)for(let r=0;r<e.props.length;r++){const o=e.props[r];if(7===o.type&&"bind"===o.name&&o.exp){const r=getConstantType(o.exp,t);if(0===r)return n.set(e,0),0;r<s&&(s=r)}}if(r.isBlock){for(let t=0;t<e.props.length;t++){if(7===e.props[t].type)return n.set(e,0),0}t.removeHelper(OPEN_BLOCK),t.removeHelper(getVNodeBlockHelper(t.inSSR,r.isComponent)),r.isBlock=!1,t.helper(getVNodeHelper(t.inSSR,r.isComponent))}return n.set(e,s),s}return n.set(e,0),0;case 2:case 3:return 3;case 9:case 11:case 10:default:return 0;case 5:case 12:return getConstantType(e.content,t);case 4:return e.constType;case 8:let o=3;for(let n=0;n<e.children.length;n++){const s=e.children[n];if(isString(s)||isSymbol(s))continue;const r=getConstantType(s,t);if(0===r)return 0;r<o&&(o=r)}return o;case 20:return 2}}const allowHoistedHelperSet=new Set([NORMALIZE_CLASS,NORMALIZE_STYLE,NORMALIZE_PROPS,GUARD_REACTIVE_PROPS]);function getConstantTypeOfHelperCall(e,t){if(14===e.type&&!isString(e.callee)&&allowHoistedHelperSet.has(e.callee)){const n=e.arguments[0];if(4===n.type)return getConstantType(n,t);if(14===n.type)return getConstantTypeOfHelperCall(n,t)}return 0}function getGeneratedPropsConstantType(e,t){let n=3;const s=getNodeProps(e);if(s&&15===s.type){const{properties:e}=s;for(let s=0;s<e.length;s++){const{key:r,value:o}=e[s],i=getConstantType(r,t);if(0===i)return i;let a;if(i<n&&(n=i),a=4===o.type?getConstantType(o,t):14===o.type?getConstantTypeOfHelperCall(o,t):0,0===a)return a;a<n&&(n=a)}}return n}function getNodeProps(e){const t=e.codegenNode;if(13===t.type)return t.props}function createTransformContext(e,{filename:t="",prefixIdentifiers:n=!1,hoistStatic:s=!1,hmr:r=!1,cacheHandlers:o=!1,nodeTransforms:i=[],directiveTransforms:a={},transformHoist:c=null,isBuiltInComponent:l=NOOP,isCustomElement:d=NOOP,expressionPlugins:u=[],scopeId:p=null,slotted:h=!0,ssr:f=!1,inSSR:m=!1,ssrCssVars:g="",bindingMetadata:y=EMPTY_OBJ,inline:v=!1,isTS:S=!1,onError:_=defaultOnError,onWarn:b=defaultOnWarn,compatConfig:E}){const C=t.replace(/\?.*$/,"").match(/([^/\\]+)\.\w+$/),T={filename:t,selfName:C&&capitalize(camelize(C[1])),prefixIdentifiers:n,hoistStatic:s,hmr:r,cacheHandlers:o,nodeTransforms:i,directiveTransforms:a,transformHoist:c,isBuiltInComponent:l,isCustomElement:d,expressionPlugins:u,scopeId:p,slotted:h,ssr:f,inSSR:m,ssrCssVars:g,bindingMetadata:y,inline:v,isTS:S,onError:_,onWarn:b,compatConfig:E,root:e,helpers:new Map,components:new Set,directives:new Set,hoists:[],imports:[],cached:[],constantCache:new WeakMap,temps:0,identifiers:Object.create(null),scopes:{vFor:0,vSlot:0,vPre:0,vOnce:0},parent:null,grandParent:null,currentNode:e,childIndex:0,inVOnce:!1,helper(e){const t=T.helpers.get(e)||0;return T.helpers.set(e,t+1),e},removeHelper(e){const t=T.helpers.get(e);if(t){const n=t-1;n?T.helpers.set(e,n):T.helpers.delete(e)}},helperString:e=>`_${helperNameMap[T.helper(e)]}`,replaceNode(e){T.parent.children[T.childIndex]=T.currentNode=e},removeNode(e){const t=T.parent.children,n=e?t.indexOf(e):T.currentNode?T.childIndex:-1;e&&e!==T.currentNode?T.childIndex>n&&(T.childIndex--,T.onNodeRemoved()):(T.currentNode=null,T.onNodeRemoved()),T.parent.children.splice(n,1)},onNodeRemoved:NOOP,addIdentifiers(e){},removeIdentifiers(e){},hoist(e){isString(e)&&(e=createSimpleExpression(e)),T.hoists.push(e);const t=createSimpleExpression(`_hoisted_${T.hoists.length}`,!1,e.loc,2);return t.hoisted=e,t},cache(e,t=!1,n=!1){const s=createCacheExpression(T.cached.length,e,t,n);return T.cached.push(s),s}};return T.filters=new Set,T}function transform(e,t){const n=createTransformContext(e,t);traverseNode(e,n),t.hoistStatic&&cacheStatic(e,n),t.ssr||createRootCodegen(e,n),e.helpers=new Set([...n.helpers.keys()]),e.components=[...n.components],e.directives=[...n.directives],e.imports=n.imports,e.hoists=n.hoists,e.temps=n.temps,e.cached=n.cached,e.transformed=!0,e.filters=[...n.filters]}function createRootCodegen(e,t){const{helper:n}=t,{children:s}=e;if(1===s.length){const n=getSingleElementRoot(e);if(n&&n.codegenNode){const s=n.codegenNode;13===s.type&&convertToBlock(s,t),e.codegenNode=s}else e.codegenNode=s[0]}else if(s.length>1){let s=64;e.codegenNode=createVNodeCall(t,n(FRAGMENT),void 0,e.children,s,void 0,void 0,!0,void 0,!1)}}function traverseChildren(e,t){let n=0;const s=()=>{n--};for(;n<e.children.length;n++){const r=e.children[n];isString(r)||(t.grandParent=t.parent,t.parent=e,t.childIndex=n,t.onNodeRemoved=s,traverseNode(r,t))}}function traverseNode(e,t){t.currentNode=e;const{nodeTransforms:n}=t,s=[];for(let o=0;o<n.length;o++){const r=n[o](e,t);if(r&&(isArray(r)?s.push(...r):s.push(r)),!t.currentNode)return;e=t.currentNode}switch(e.type){case 3:t.ssr||t.helper(CREATE_COMMENT);break;case 5:t.ssr||t.helper(TO_DISPLAY_STRING);break;case 9:for(let n=0;n<e.branches.length;n++)traverseNode(e.branches[n],t);break;case 10:case 11:case 1:case 0:traverseChildren(e,t)}t.currentNode=e;let r=s.length;for(;r--;)s[r]()}function createStructuralDirectiveTransform(e,t){const n=isString(e)?t=>t===e:t=>e.test(t);return(e,s)=>{if(1===e.type){const{props:r}=e;if(3===e.tagType&&r.some(isVSlot))return;const o=[];for(let i=0;i<r.length;i++){const a=r[i];if(7===a.type&&n(a.name)){r.splice(i,1),i--;const n=t(e,a,s);n&&o.push(n)}}return o}}}const PURE_ANNOTATION="/*@__PURE__*/",aliasHelper=e=>`${helperNameMap[e]}: _${helperNameMap[e]}`;function createCodegenContext(e,{mode:t="function",prefixIdentifiers:n="module"===t,sourceMap:s=!1,filename:r="template.vue.html",scopeId:o=null,optimizeImports:i=!1,runtimeGlobalName:a="Vue",runtimeModuleName:c="vue",ssrRuntimeModuleName:l="vue/server-renderer",ssr:d=!1,isTS:u=!1,inSSR:p=!1}){const h={mode:t,prefixIdentifiers:n,sourceMap:s,filename:r,scopeId:o,optimizeImports:i,runtimeGlobalName:a,runtimeModuleName:c,ssrRuntimeModuleName:l,ssr:d,isTS:u,inSSR:p,source:e.source,code:"",column:1,line:1,offset:0,indentLevel:0,pure:!1,map:void 0,helper:e=>`_${helperNameMap[e]}`,push(e,t=-2,n){h.code+=e},indent(){f(++h.indentLevel)},deindent(e=!1){e?--h.indentLevel:f(--h.indentLevel)},newline(){f(h.indentLevel)}};function f(e){h.push("\n"+"  ".repeat(e),0)}return h}function generate(e,t={}){const n=createCodegenContext(e,t);t.onContextCreated&&t.onContextCreated(n);const{mode:s,push:r,prefixIdentifiers:o,indent:i,deindent:a,newline:c,scopeId:l,ssr:d}=n,u=Array.from(e.helpers),p=u.length>0,h=!o&&"module"!==s;genFunctionPreamble(e,n);if(r(`function ${d?"ssrRender":"render"}(${(d?["_ctx","_push","_parent","_attrs"]:["_ctx","_cache"]).join(", ")}) {`),i(),h&&(r("with (_ctx) {"),i(),p&&(r(`const { ${u.map(aliasHelper).join(", ")} } = _Vue\n`,-1),c())),e.components.length&&(genAssets(e.components,"component",n),(e.directives.length||e.temps>0)&&c()),e.directives.length&&(genAssets(e.directives,"directive",n),e.temps>0&&c()),e.filters&&e.filters.length&&(c(),genAssets(e.filters,"filter",n),c()),e.temps>0){r("let ");for(let t=0;t<e.temps;t++)r(`${t>0?", ":""}_temp${t}`)}return(e.components.length||e.directives.length||e.temps)&&(r("\n",0),c()),d||r("return "),e.codegenNode?genNode(e.codegenNode,n):r("null"),h&&(a(),r("}")),a(),r("}"),{ast:e,code:n.code,preamble:"",map:n.map?n.map.toJSON():void 0}}function genFunctionPreamble(e,t){const{ssr:n,prefixIdentifiers:s,push:r,newline:o,runtimeModuleName:i,runtimeGlobalName:a,ssrRuntimeModuleName:c}=t,l=a,d=Array.from(e.helpers);if(d.length>0&&(r(`const _Vue = ${l}\n`,-1),e.hoists.length)){r(`const { ${[CREATE_VNODE,CREATE_ELEMENT_VNODE,CREATE_COMMENT,CREATE_TEXT,CREATE_STATIC].filter(e=>d.includes(e)).map(aliasHelper).join(", ")} } = _Vue\n`,-1)}genHoists(e.hoists,t),o(),r("return ")}function genAssets(e,t,{helper:n,push:s,newline:r,isTS:o}){const i=n("filter"===t?RESOLVE_FILTER:"component"===t?RESOLVE_COMPONENT:RESOLVE_DIRECTIVE);for(let a=0;a<e.length;a++){let n=e[a];const c=n.endsWith("__self");c&&(n=n.slice(0,-6)),s(`const ${toValidAssetId(n,t)} = ${i}(${JSON.stringify(n)}${c?", true":""})${o?"!":""}`),a<e.length-1&&r()}}function genHoists(e,t){if(!e.length)return;t.pure=!0;const{push:n,newline:s}=t;s();for(let r=0;r<e.length;r++){const o=e[r];o&&(n(`const _hoisted_${r+1} = `),genNode(o,t),s())}t.pure=!1}function genNodeListAsArray(e,t){const n=e.length>3||!1;t.push("["),n&&t.indent(),genNodeList(e,t,n),n&&t.deindent(),t.push("]")}function genNodeList(e,t,n=!1,s=!0){const{push:r,newline:o}=t;for(let i=0;i<e.length;i++){const a=e[i];isString(a)?r(a,-3):isArray(a)?genNodeListAsArray(a,t):genNode(a,t),i<e.length-1&&(n?(s&&r(","),o()):s&&r(", "))}}function genNode(e,t){if(isString(e))t.push(e,-3);else if(isSymbol(e))t.push(t.helper(e));else switch(e.type){case 1:case 9:case 11:case 12:genNode(e.codegenNode,t);break;case 2:genText(e,t);break;case 4:genExpression(e,t);break;case 5:genInterpolation(e,t);break;case 8:genCompoundExpression(e,t);break;case 3:genComment(e,t);break;case 13:genVNodeCall(e,t);break;case 14:genCallExpression(e,t);break;case 15:genObjectExpression(e,t);break;case 17:genArrayExpression(e,t);break;case 18:genFunctionExpression(e,t);break;case 19:genConditionalExpression(e,t);break;case 20:genCacheExpression(e,t);break;case 21:genNodeList(e.body,t,!0,!1)}}function genText(e,t){t.push(JSON.stringify(e.content),-3,e)}function genExpression(e,t){const{content:n,isStatic:s}=e;t.push(s?JSON.stringify(n):n,-3,e)}function genInterpolation(e,t){const{push:n,helper:s,pure:r}=t;r&&n(PURE_ANNOTATION),n(`${s(TO_DISPLAY_STRING)}(`),genNode(e.content,t),n(")")}function genCompoundExpression(e,t){for(let n=0;n<e.children.length;n++){const s=e.children[n];isString(s)?t.push(s,-3):genNode(s,t)}}function genExpressionAsPropertyKey(e,t){const{push:n}=t;if(8===e.type)n("["),genCompoundExpression(e,t),n("]");else if(e.isStatic){n(isSimpleIdentifier(e.content)?e.content:JSON.stringify(e.content),-2,e)}else n(`[${e.content}]`,-3,e)}function genComment(e,t){const{push:n,helper:s,pure:r}=t;r&&n(PURE_ANNOTATION),n(`${s(CREATE_COMMENT)}(${JSON.stringify(e.content)})`,-3,e)}function genVNodeCall(e,t){const{push:n,helper:s,pure:r}=t,{tag:o,props:i,children:a,patchFlag:c,dynamicProps:l,directives:d,isBlock:u,disableTracking:p,isComponent:h}=e;let f;c&&(f=String(c)),d&&n(s(WITH_DIRECTIVES)+"("),u&&n(`(${s(OPEN_BLOCK)}(${p?"true":""}), `),r&&n(PURE_ANNOTATION);n(s(u?getVNodeBlockHelper(t.inSSR,h):getVNodeHelper(t.inSSR,h))+"(",-2,e),genNodeList(genNullableArgs([o,i,a,f,l]),t),n(")"),u&&n(")"),d&&(n(", "),genNode(d,t),n(")"))}function genNullableArgs(e){let t=e.length;for(;t--&&null==e[t];);return e.slice(0,t+1).map(e=>e||"null")}function genCallExpression(e,t){const{push:n,helper:s,pure:r}=t,o=isString(e.callee)?e.callee:s(e.callee);r&&n(PURE_ANNOTATION),n(o+"(",-2,e),genNodeList(e.arguments,t),n(")")}function genObjectExpression(e,t){const{push:n,indent:s,deindent:r,newline:o}=t,{properties:i}=e;if(!i.length)return void n("{}",-2,e);const a=i.length>1||!1;n(a?"{":"{ "),a&&s();for(let c=0;c<i.length;c++){const{key:e,value:s}=i[c];genExpressionAsPropertyKey(e,t),n(": "),genNode(s,t),c<i.length-1&&(n(","),o())}a&&r(),n(a?"}":" }")}function genArrayExpression(e,t){genNodeListAsArray(e.elements,t)}function genFunctionExpression(e,t){const{push:n,indent:s,deindent:r}=t,{params:o,returns:i,body:a,newline:c,isSlot:l}=e;l&&n(`_${helperNameMap[WITH_CTX]}(`),n("(",-2,e),isArray(o)?genNodeList(o,t):o&&genNode(o,t),n(") => "),(c||a)&&(n("{"),s()),i?(c&&n("return "),isArray(i)?genNodeListAsArray(i,t):genNode(i,t)):a&&genNode(a,t),(c||a)&&(r(),n("}")),l&&(e.isNonScopedSlot&&n(", undefined, true"),n(")"))}function genConditionalExpression(e,t){const{test:n,consequent:s,alternate:r,newline:o}=e,{push:i,indent:a,deindent:c,newline:l}=t;if(4===n.type){const e=!isSimpleIdentifier(n.content);e&&i("("),genExpression(n,t),e&&i(")")}else i("("),genNode(n,t),i(")");o&&a(),t.indentLevel++,o||i(" "),i("? "),genNode(s,t),t.indentLevel--,o&&l(),o||i(" "),i(": ");const d=19===r.type;d||t.indentLevel++,genNode(r,t),d||t.indentLevel--,o&&c(!0)}function genCacheExpression(e,t){const{push:n,helper:s,indent:r,deindent:o,newline:i}=t,{needPauseTracking:a,needArraySpread:c}=e;c&&n("[...("),n(`_cache[${e.index}] || (`),a&&(r(),n(`${s(SET_BLOCK_TRACKING)}(-1`),e.inVOnce&&n(", true"),n("),"),i(),n("(")),n(`_cache[${e.index}] = `),genNode(e.value,t),a&&(n(`).cacheIndex = ${e.index},`),i(),n(`${s(SET_BLOCK_TRACKING)}(1),`),i(),n(`_cache[${e.index}]`),o()),n(")"),c&&n(")]")}new RegExp("\\b"+"arguments,await,break,case,catch,class,const,continue,debugger,default,delete,do,else,export,extends,finally,for,function,if,import,let,new,return,super,switch,throw,try,var,void,while,with,yield".split(",").join("\\b|\\b")+"\\b");const transformIf=createStructuralDirectiveTransform(/^(if|else|else-if)$/,(e,t,n)=>processIf(e,t,n,(e,t,s)=>{const r=n.parent.children;let o=r.indexOf(e),i=0;for(;o-- >=0;){const e=r[o];e&&9===e.type&&(i+=e.branches.length)}return()=>{if(s)e.codegenNode=createCodegenNodeForBranch(t,i,n);else{getParentCondition(e.codegenNode).alternate=createCodegenNodeForBranch(t,i+e.branches.length-1,n)}}}));function processIf(e,t,n,s){if(!("else"===t.name||t.exp&&t.exp.content.trim())){const s=t.exp?t.exp.loc:e.loc;n.onError(createCompilerError(28,t.loc)),t.exp=createSimpleExpression("true",!1,s)}if("if"===t.name){const r=createIfBranch(e,t),o={type:9,loc:cloneLoc(e.loc),branches:[r]};if(n.replaceNode(o),s)return s(o,r,!0)}else{const r=n.parent.children;let o=r.indexOf(e);for(;o-- >=-1;){const i=r[o];if(i&&3===i.type)n.removeNode(i);else{if(!i||2!==i.type||i.content.trim().length){if(i&&9===i.type){"else-if"===t.name&&void 0===i.branches[i.branches.length-1].condition&&n.onError(createCompilerError(30,e.loc)),n.removeNode();const r=createIfBranch(e,t);i.branches.push(r);const o=s&&s(i,r,!1);traverseNode(r,n),o&&o(),n.currentNode=null}else n.onError(createCompilerError(30,e.loc));break}n.removeNode(i)}}}}function createIfBranch(e,t){const n=3===e.tagType;return{type:10,loc:e.loc,condition:"else"===t.name?void 0:t.exp,children:n&&!findDir(e,"for")?e.children:[e],userKey:findProp(e,"key"),isTemplateIf:n}}function createCodegenNodeForBranch(e,t,n){return e.condition?createConditionalExpression(e.condition,createChildrenCodegenNode(e,t,n),createCallExpression(n.helper(CREATE_COMMENT),['""',"true"])):createChildrenCodegenNode(e,t,n)}function createChildrenCodegenNode(e,t,n){const{helper:s}=n,r=createObjectProperty("key",createSimpleExpression(`${t}`,!1,locStub,2)),{children:o}=e,i=o[0];if(1!==o.length||1!==i.type){if(1===o.length&&11===i.type){const e=i.codegenNode;return injectProp(e,r,n),e}{let t=64;return createVNodeCall(n,s(FRAGMENT),createObjectExpression([r]),o,t,void 0,void 0,!0,!1,!1,e.loc)}}{const e=i.codegenNode,t=getMemoedVNodeCall(e);return 13===t.type&&convertToBlock(t,n),injectProp(t,r,n),e}}function getParentCondition(e){for(;;)if(19===e.type){if(19!==e.alternate.type)return e;e=e.alternate}else 20===e.type&&(e=e.value)}const transformBind=(e,t,n)=>{const{modifiers:s,loc:r}=e,o=e.arg;let{exp:i}=e;if(i&&4===i.type&&!i.content.trim()&&(i=void 0),!i){if(4!==o.type||!o.isStatic)return n.onError(createCompilerError(52,o.loc)),{props:[createObjectProperty(o,createSimpleExpression("",!0,r))]};transformBindShorthand(e),i=e.exp}return 4!==o.type?(o.children.unshift("("),o.children.push(') || ""')):o.isStatic||(o.content=o.content?`${o.content} || ""`:'""'),s.some(e=>"camel"===e.content)&&(4===o.type?o.isStatic?o.content=camelize(o.content):o.content=`${n.helperString(CAMELIZE)}(${o.content})`:(o.children.unshift(`${n.helperString(CAMELIZE)}(`),o.children.push(")"))),n.inSSR||(s.some(e=>"prop"===e.content)&&injectPrefix(o,"."),s.some(e=>"attr"===e.content)&&injectPrefix(o,"^")),{props:[createObjectProperty(o,i)]}},transformBindShorthand=(e,t)=>{const n=e.arg,s=camelize(n.content);e.exp=createSimpleExpression(s,!1,n.loc)},injectPrefix=(e,t)=>{4===e.type?e.isStatic?e.content=t+e.content:e.content=`\`${t}\${${e.content}}\``:(e.children.unshift(`'${t}' + (`),e.children.push(")"))},transformFor=createStructuralDirectiveTransform("for",(e,t,n)=>{const{helper:s,removeHelper:r}=n;return processFor(e,t,n,t=>{const o=createCallExpression(s(RENDER_LIST),[t.source]),i=isTemplateNode(e),a=findDir(e,"memo"),c=findProp(e,"key",!1,!0);c&&7===c.type&&!c.exp&&transformBindShorthand(c);let l=c&&(6===c.type?c.value?createSimpleExpression(c.value.content,!0):void 0:c.exp);const d=c&&l?createObjectProperty("key",l):null,u=4===t.source.type&&t.source.constType>0,p=u?64:c?128:256;return t.codegenNode=createVNodeCall(n,s(FRAGMENT),void 0,o,p,void 0,void 0,!0,!u,!1,e.loc),()=>{let c;const{children:p}=t,h=1!==p.length||1!==p[0].type,f=isSlotOutlet(e)?e:i&&1===e.children.length&&isSlotOutlet(e.children[0])?e.children[0]:null;if(f?(c=f.codegenNode,i&&d&&injectProp(c,d,n)):h?c=createVNodeCall(n,s(FRAGMENT),d?createObjectExpression([d]):void 0,e.children,64,void 0,void 0,!0,void 0,!1):(c=p[0].codegenNode,i&&d&&injectProp(c,d,n),c.isBlock!==!u&&(c.isBlock?(r(OPEN_BLOCK),r(getVNodeBlockHelper(n.inSSR,c.isComponent))):r(getVNodeHelper(n.inSSR,c.isComponent))),c.isBlock=!u,c.isBlock?(s(OPEN_BLOCK),s(getVNodeBlockHelper(n.inSSR,c.isComponent))):s(getVNodeHelper(n.inSSR,c.isComponent))),a){const e=createFunctionExpression(createForLoopParams(t.parseResult,[createSimpleExpression("_cached")]));e.body=createBlockStatement([createCompoundExpression(["const _memo = (",a.exp,")"]),createCompoundExpression(["if (_cached",...l?[" && _cached.key === ",l]:[],` && ${n.helperString(IS_MEMO_SAME)}(_cached, _memo)) return _cached`]),createCompoundExpression(["const _item = ",c]),createSimpleExpression("_item.memo = _memo"),createSimpleExpression("return _item")]),o.arguments.push(e,createSimpleExpression("_cache"),createSimpleExpression(String(n.cached.length))),n.cached.push(null)}else o.arguments.push(createFunctionExpression(createForLoopParams(t.parseResult),c,!0))}})});function processFor(e,t,n,s){if(!t.exp)return void n.onError(createCompilerError(31,t.loc));const r=t.forParseResult;if(!r)return void n.onError(createCompilerError(32,t.loc));finalizeForParseResult(r);const{addIdentifiers:o,removeIdentifiers:i,scopes:a}=n,{source:c,value:l,key:d,index:u}=r,p={type:11,loc:t.loc,source:c,valueAlias:l,keyAlias:d,objectIndexAlias:u,parseResult:r,children:isTemplateNode(e)?e.children:[e]};n.replaceNode(p),a.vFor++;const h=s&&s(p);return()=>{a.vFor--,h&&h()}}function finalizeForParseResult(e,t){e.finalized||(e.finalized=!0)}function createForLoopParams({value:e,key:t,index:n},s=[]){return createParamsList([e,t,n,...s])}function createParamsList(e){let t=e.length;for(;t--&&!e[t];);return e.slice(0,t+1).map((e,t)=>e||createSimpleExpression("_".repeat(t+1),!1))}const defaultFallback=createSimpleExpression("undefined",!1),trackSlotScopes=(e,t)=>{if(1===e.type&&(1===e.tagType||3===e.tagType)){const n=findDir(e,"slot");if(n)return n.exp,t.scopes.vSlot++,()=>{t.scopes.vSlot--}}},buildClientSlotFn=(e,t,n,s)=>createFunctionExpression(e,n,!1,!0,n.length?n[0].loc:s);function buildSlots(e,t,n=buildClientSlotFn){t.helper(WITH_CTX);const{children:s,loc:r}=e,o=[],i=[];let a=t.scopes.vSlot>0||t.scopes.vFor>0;const c=findDir(e,"slot",!0);if(c){const{arg:e,exp:t}=c;e&&!isStaticExp(e)&&(a=!0),o.push(createObjectProperty(e||createSimpleExpression("default",!0),n(t,void 0,s,r)))}let l=!1,d=!1;const u=[],p=new Set;let h=0;for(let g=0;g<s.length;g++){const e=s[g];let r;if(!isTemplateNode(e)||!(r=findDir(e,"slot",!0))){3!==e.type&&u.push(e);continue}if(c){t.onError(createCompilerError(37,r.loc));break}l=!0;const{children:f,loc:m}=e,{arg:y=createSimpleExpression("default",!0),exp:v,loc:S}=r;let _;isStaticExp(y)?_=y?y.content:"default":a=!0;const b=findDir(e,"for"),E=n(v,b,f,m);let C,T;if(C=findDir(e,"if"))a=!0,i.push(createConditionalExpression(C.exp,buildDynamicSlot(y,E,h++),defaultFallback));else if(T=findDir(e,/^else(-if)?$/,!0)){let e,n=g;for(;n--&&(e=s[n],3===e.type||!isNonWhitespaceContent(e)););if(e&&isTemplateNode(e)&&findDir(e,/^(else-)?if$/)){let e=i[i.length-1];for(;19===e.alternate.type;)e=e.alternate;e.alternate=T.exp?createConditionalExpression(T.exp,buildDynamicSlot(y,E,h++),defaultFallback):buildDynamicSlot(y,E,h++)}else t.onError(createCompilerError(30,T.loc))}else if(b){a=!0;const e=b.forParseResult;e?(finalizeForParseResult(e),i.push(createCallExpression(t.helper(RENDER_LIST),[e.source,createFunctionExpression(createForLoopParams(e),buildDynamicSlot(y,E),!0)]))):t.onError(createCompilerError(32,b.loc))}else{if(_){if(p.has(_)){t.onError(createCompilerError(38,S));continue}p.add(_),"default"===_&&(d=!0)}o.push(createObjectProperty(y,E))}}if(!c){const e=(e,s)=>{const o=n(e,void 0,s,r);return t.compatConfig&&(o.isNonScopedSlot=!0),createObjectProperty("default",o)};l?u.length&&u.some(e=>isNonWhitespaceContent(e))&&(d?t.onError(createCompilerError(39,u[0].loc)):o.push(e(void 0,u))):o.push(e(void 0,s))}const f=a?2:hasForwardedSlots(e.children)?3:1;let m=createObjectExpression(o.concat(createObjectProperty("_",createSimpleExpression(f+"",!1))),r);return i.length&&(m=createCallExpression(t.helper(CREATE_SLOTS),[m,createArrayExpression(i)])),{slots:m,hasDynamicSlots:a}}function buildDynamicSlot(e,t,n){const s=[createObjectProperty("name",e),createObjectProperty("fn",t)];return null!=n&&s.push(createObjectProperty("key",createSimpleExpression(String(n),!0))),createObjectExpression(s)}function hasForwardedSlots(e){for(let t=0;t<e.length;t++){const n=e[t];switch(n.type){case 1:if(2===n.tagType||hasForwardedSlots(n.children))return!0;break;case 9:if(hasForwardedSlots(n.branches))return!0;break;case 10:case 11:if(hasForwardedSlots(n.children))return!0}}return!1}function isNonWhitespaceContent(e){return 2!==e.type&&12!==e.type||(2===e.type?!!e.content.trim():isNonWhitespaceContent(e.content))}const directiveImportMap=new WeakMap,transformElement=(e,t)=>function(){if(1!==(e=t.currentNode).type||0!==e.tagType&&1!==e.tagType)return;const{tag:n,props:s}=e,r=1===e.tagType;let o=r?resolveComponentType(e,t):`"${n}"`;const i=isObject(o)&&o.callee===RESOLVE_DYNAMIC_COMPONENT;let a,c,l,d,u,p=0,h=i||o===TELEPORT||o===SUSPENSE||!r&&("svg"===n||"foreignObject"===n||"math"===n);if(s.length>0){const n=buildProps(e,t,void 0,r,i);a=n.props,p=n.patchFlag,d=n.dynamicPropNames;const s=n.directives;u=s&&s.length?createArrayExpression(s.map(e=>buildDirectiveArgs(e,t))):void 0,n.shouldUseBlock&&(h=!0)}if(e.children.length>0){o===KEEP_ALIVE&&(h=!0,p|=1024);if(r&&o!==TELEPORT&&o!==KEEP_ALIVE){const{slots:n,hasDynamicSlots:s}=buildSlots(e,t);c=n,s&&(p|=1024)}else if(1===e.children.length&&o!==TELEPORT){const n=e.children[0],s=n.type,r=5===s||8===s;r&&0===getConstantType(n,t)&&(p|=1),c=r||2===s?n:e.children}else c=e.children}d&&d.length&&(l=stringifyDynamicPropNames(d)),e.codegenNode=createVNodeCall(t,o,a,c,0===p?void 0:p,l,u,!!h,!1,r,e.loc)};function resolveComponentType(e,t,n=!1){let{tag:s}=e;const r=isComponentTag(s),o=findProp(e,"is",!1,!0);if(o)if(r||isCompatEnabled("COMPILER_IS_ON_ELEMENT",t)){let e;if(6===o.type?e=o.value&&createSimpleExpression(o.value.content,!0):(e=o.exp,e||(e=createSimpleExpression("is",!1,o.arg.loc))),e)return createCallExpression(t.helper(RESOLVE_DYNAMIC_COMPONENT),[e])}else 6===o.type&&o.value.content.startsWith("vue:")&&(s=o.value.content.slice(4));const i=isCoreComponent(s)||t.isBuiltInComponent(s);return i?(n||t.helper(i),i):(t.helper(RESOLVE_COMPONENT),t.components.add(s),toValidAssetId(s,"component"))}function buildProps(e,t,n=e.props,s,r,o=!1){const{tag:i,loc:a,children:c}=e;let l=[];const d=[],u=[],p=c.length>0;let h=!1,f=0,m=!1,g=!1,y=!1,v=!1,S=!1,_=!1;const b=[],E=e=>{l.length&&(d.push(createObjectExpression(dedupeProperties(l),a)),l=[]),e&&d.push(e)},C=()=>{t.scopes.vFor>0&&l.push(createObjectProperty(createSimpleExpression("ref_for",!0),createSimpleExpression("true")))},T=({key:e,value:n})=>{if(isStaticExp(e)){const o=e.content,i=isOn(o);if(!i||s&&!r||"onclick"===o.toLowerCase()||"onUpdate:modelValue"===o||isReservedProp(o)||(v=!0),i&&isReservedProp(o)&&(_=!0),i&&14===n.type&&(n=n.arguments[0]),20===n.type||(4===n.type||8===n.type)&&getConstantType(n,t)>0)return;"ref"===o?m=!0:"class"===o?g=!0:"style"===o?y=!0:"key"===o||b.includes(o)||b.push(o),!s||"class"!==o&&"style"!==o||b.includes(o)||b.push(o)}else S=!0};for(let A=0;A<n.length;A++){const r=n[A];if(6===r.type){const{loc:e,name:n,nameLoc:s,value:o}=r;let a=!0;if("ref"===n&&(m=!0,C()),"is"===n&&(isComponentTag(i)||o&&o.content.startsWith("vue:")||isCompatEnabled("COMPILER_IS_ON_ELEMENT",t)))continue;l.push(createObjectProperty(createSimpleExpression(n,!0,s),createSimpleExpression(o?o.content:"",a,o?o.loc:e)))}else{const{name:n,arg:c,exp:m,loc:g,modifiers:y}=r,v="bind"===n,_="on"===n;if("slot"===n){s||t.onError(createCompilerError(40,g));continue}if("once"===n||"memo"===n)continue;if("is"===n||v&&isStaticArgOf(c,"is")&&(isComponentTag(i)||isCompatEnabled("COMPILER_IS_ON_ELEMENT",t)))continue;if(_&&o)continue;if((v&&isStaticArgOf(c,"key")||_&&p&&isStaticArgOf(c,"vue:before-update"))&&(h=!0),v&&isStaticArgOf(c,"ref")&&C(),!c&&(v||_)){if(S=!0,m)if(v){if(E(),isCompatEnabled("COMPILER_V_BIND_OBJECT_ORDER",t)){d.unshift(m);continue}C(),E(),d.push(m)}else E({type:14,loc:g,callee:t.helper(TO_HANDLERS),arguments:s?[m]:[m,"true"]});else t.onError(createCompilerError(v?34:35,g));continue}v&&y.some(e=>"prop"===e.content)&&(f|=32);const b=t.directiveTransforms[n];if(b){const{props:n,needRuntime:s}=b(r,e,t);!o&&n.forEach(T),_&&c&&!isStaticExp(c)?E(createObjectExpression(n,a)):l.push(...n),s&&(u.push(r),isSymbol(s)&&directiveImportMap.set(r,s))}else isBuiltInDirective(n)||(u.push(r),p&&(h=!0))}}let N;if(d.length?(E(),N=d.length>1?createCallExpression(t.helper(MERGE_PROPS),d,a):d[0]):l.length&&(N=createObjectExpression(dedupeProperties(l),a)),S?f|=16:(g&&!s&&(f|=2),y&&!s&&(f|=4),b.length&&(f|=8),v&&(f|=32)),h||0!==f&&32!==f||!(m||_||u.length>0)||(f|=512),!t.inSSR&&N)switch(N.type){case 15:let e=-1,n=-1,s=!1;for(let t=0;t<N.properties.length;t++){const r=N.properties[t].key;isStaticExp(r)?"class"===r.content?e=t:"style"===r.content&&(n=t):r.isHandlerKey||(s=!0)}const r=N.properties[e],o=N.properties[n];s?N=createCallExpression(t.helper(NORMALIZE_PROPS),[N]):(r&&!isStaticExp(r.value)&&(r.value=createCallExpression(t.helper(NORMALIZE_CLASS),[r.value])),o&&(y||4===o.value.type&&"["===o.value.content.trim()[0]||17===o.value.type)&&(o.value=createCallExpression(t.helper(NORMALIZE_STYLE),[o.value])));break;case 14:break;default:N=createCallExpression(t.helper(NORMALIZE_PROPS),[createCallExpression(t.helper(GUARD_REACTIVE_PROPS),[N])])}return{props:N,directives:u,patchFlag:f,dynamicPropNames:b,shouldUseBlock:h}}function dedupeProperties(e){const t=new Map,n=[];for(let s=0;s<e.length;s++){const r=e[s];if(8===r.key.type||!r.key.isStatic){n.push(r);continue}const o=r.key.content,i=t.get(o);i?("style"===o||"class"===o||isOn(o))&&mergeAsArray(i,r):(t.set(o,r),n.push(r))}return n}function mergeAsArray(e,t){17===e.value.type?e.value.elements.push(t.value):e.value=createArrayExpression([e.value,t.value],e.loc)}function buildDirectiveArgs(e,t){const n=[],s=directiveImportMap.get(e);s?n.push(t.helperString(s)):(t.helper(RESOLVE_DIRECTIVE),t.directives.add(e.name),n.push(toValidAssetId(e.name,"directive")));const{loc:r}=e;if(e.exp&&n.push(e.exp),e.arg&&(e.exp||n.push("void 0"),n.push(e.arg)),Object.keys(e.modifiers).length){e.arg||(e.exp||n.push("void 0"),n.push("void 0"));const t=createSimpleExpression("true",!1,r);n.push(createObjectExpression(e.modifiers.map(e=>createObjectProperty(e,t)),r))}return createArrayExpression(n,e.loc)}function stringifyDynamicPropNames(e){let t="[";for(let n=0,s=e.length;n<s;n++)t+=JSON.stringify(e[n]),n<s-1&&(t+=", ");return t+"]"}function isComponentTag(e){return"component"===e||"Component"===e}const transformSlotOutlet=(e,t)=>{if(isSlotOutlet(e)){const{children:n,loc:s}=e,{slotName:r,slotProps:o}=processSlotOutlet(e,t),i=[t.prefixIdentifiers?"_ctx.$slots":"$slots",r,"{}","undefined","true"];let a=2;o&&(i[2]=o,a=3),n.length&&(i[3]=createFunctionExpression([],n,!1,!1,s),a=4),t.scopeId&&!t.slotted&&(a=5),i.splice(a),e.codegenNode=createCallExpression(t.helper(RENDER_SLOT),i,s)}};function processSlotOutlet(e,t){let n,s='"default"';const r=[];for(let o=0;o<e.props.length;o++){const t=e.props[o];if(6===t.type)t.value&&("name"===t.name?s=JSON.stringify(t.value.content):(t.name=camelize(t.name),r.push(t)));else if("bind"===t.name&&isStaticArgOf(t.arg,"name")){if(t.exp)s=t.exp;else if(t.arg&&4===t.arg.type){const e=camelize(t.arg.content);s=t.exp=createSimpleExpression(e,!1,t.arg.loc)}}else"bind"===t.name&&t.arg&&isStaticExp(t.arg)&&(t.arg.content=camelize(t.arg.content)),r.push(t)}if(r.length>0){const{props:s,directives:o}=buildProps(e,t,r,!1,!1);n=s,o.length&&t.onError(createCompilerError(36,o[0].loc))}return{slotName:s,slotProps:n}}const transformOn$1=(e,t,n,s)=>{const{loc:r,modifiers:o,arg:i}=e;let a;if(e.exp||o.length||n.onError(createCompilerError(35,r)),4===i.type)if(i.isStatic){let e=i.content;e.startsWith("vue:")&&(e=`vnode-${e.slice(4)}`);a=createSimpleExpression(0!==t.tagType||e.startsWith("vnode")||!/[A-Z]/.test(e)?toHandlerKey(camelize(e)):`on:${e}`,!0,i.loc)}else a=createCompoundExpression([`${n.helperString(TO_HANDLER_KEY)}(`,i,")"]);else a=i,a.children.unshift(`${n.helperString(TO_HANDLER_KEY)}(`),a.children.push(")");let c=e.exp;c&&!c.content.trim()&&(c=void 0);let l=n.cacheHandlers&&!c&&!n.inVOnce;if(c){const e=isMemberExpression(c),t=!(e||isFnExpression(c)),n=c.content.includes(";");(t||l&&e)&&(c=createCompoundExpression([`${t?"$event":"(...args)"} => ${n?"{":"("}`,c,n?"}":")"]))}let d={props:[createObjectProperty(a,c||createSimpleExpression("() => {}",!1,r))]};return s&&(d=s(d)),l&&(d.props[0].value=n.cache(d.props[0].value)),d.props.forEach(e=>e.key.isHandlerKey=!0),d},transformText=(e,t)=>{if(0===e.type||1===e.type||11===e.type||10===e.type)return()=>{const n=e.children;let s,r=!1;for(let e=0;e<n.length;e++){const t=n[e];if(isText$1(t)){r=!0;for(let r=e+1;r<n.length;r++){const o=n[r];if(!isText$1(o)){s=void 0;break}s||(s=n[e]=createCompoundExpression([t],t.loc)),s.children.push(" + ",o),n.splice(r,1),r--}}}if(r&&(1!==n.length||0!==e.type&&(1!==e.type||0!==e.tagType||e.props.find(e=>7===e.type&&!t.directiveTransforms[e.name])||"template"===e.tag)))for(let e=0;e<n.length;e++){const s=n[e];if(isText$1(s)||8===s.type){const r=[];2===s.type&&" "===s.content||r.push(s),t.ssr||0!==getConstantType(s,t)||r.push("1"),n[e]={type:12,content:s,loc:s.loc,codegenNode:createCallExpression(t.helper(CREATE_TEXT),r)}}}}},seen$1=new WeakSet,transformOnce=(e,t)=>{if(1===e.type&&findDir(e,"once",!0)){if(seen$1.has(e)||t.inVOnce||t.inSSR)return;return seen$1.add(e),t.inVOnce=!0,t.helper(SET_BLOCK_TRACKING),()=>{t.inVOnce=!1;const e=t.currentNode;e.codegenNode&&(e.codegenNode=t.cache(e.codegenNode,!0,!0))}}},transformModel$1=(e,t,n)=>{const{exp:s,arg:r}=e;if(!s)return n.onError(createCompilerError(41,e.loc)),createTransformProps();const o=s.loc.source.trim(),i=4===s.type?s.content:o,a=n.bindingMetadata[o];if("props"===a||"props-aliased"===a)return n.onError(createCompilerError(44,s.loc)),createTransformProps();if(!i.trim()||!isMemberExpression(s))return n.onError(createCompilerError(42,s.loc)),createTransformProps();const c=r||createSimpleExpression("modelValue",!0),l=r?isStaticExp(r)?`onUpdate:${camelize(r.content)}`:createCompoundExpression(['"onUpdate:" + ',r]):"onUpdate:modelValue";let d;d=createCompoundExpression([`${n.isTS?"($event: any)":"$event"} => ((`,s,") = $event)"]);const u=[createObjectProperty(c,e.exp),createObjectProperty(l,d)];if(e.modifiers.length&&1===t.tagType){const t=e.modifiers.map(e=>e.content).map(e=>(isSimpleIdentifier(e)?e:JSON.stringify(e))+": true").join(", "),n=r?isStaticExp(r)?`${r.content}Modifiers`:createCompoundExpression([r,' + "Modifiers"']):"modelModifiers";u.push(createObjectProperty(n,createSimpleExpression(`{ ${t} }`,!1,e.loc,2)))}return createTransformProps(u)};function createTransformProps(e=[]){return{props:e}}const validDivisionCharRE=/[\w).+\-_$\]]/,transformFilter=(e,t)=>{isCompatEnabled("COMPILER_FILTERS",t)&&(5===e.type?rewriteFilter(e.content,t):1===e.type&&e.props.forEach(e=>{7===e.type&&"for"!==e.name&&e.exp&&rewriteFilter(e.exp,t)}))};function rewriteFilter(e,t){if(4===e.type)parseFilter(e,t);else for(let n=0;n<e.children.length;n++){const s=e.children[n];"object"==typeof s&&(4===s.type?parseFilter(s,t):8===s.type?rewriteFilter(e,t):5===s.type&&rewriteFilter(s.content,t))}}function parseFilter(e,t){const n=e.content;let s,r,o,i,a=!1,c=!1,l=!1,d=!1,u=0,p=0,h=0,f=0,m=[];for(o=0;o<n.length;o++)if(r=s,s=n.charCodeAt(o),a)39===s&&92!==r&&(a=!1);else if(c)34===s&&92!==r&&(c=!1);else if(l)96===s&&92!==r&&(l=!1);else if(d)47===s&&92!==r&&(d=!1);else if(124!==s||124===n.charCodeAt(o+1)||124===n.charCodeAt(o-1)||u||p||h){switch(s){case 34:c=!0;break;case 39:a=!0;break;case 96:l=!0;break;case 40:h++;break;case 41:h--;break;case 91:p++;break;case 93:p--;break;case 123:u++;break;case 125:u--}if(47===s){let e,t=o-1;for(;t>=0&&(e=n.charAt(t)," "===e);t--);e&&validDivisionCharRE.test(e)||(d=!0)}}else void 0===i?(f=o+1,i=n.slice(0,o).trim()):g();function g(){m.push(n.slice(f,o).trim()),f=o+1}if(void 0===i?i=n.slice(0,o).trim():0!==f&&g(),m.length){for(o=0;o<m.length;o++)i=wrapFilter(i,m[o],t);e.content=i,e.ast=void 0}}function wrapFilter(e,t,n){n.helper(RESOLVE_FILTER);const s=t.indexOf("(");if(s<0)return n.filters.add(t),`${toValidAssetId(t,"filter")}(${e})`;{const r=t.slice(0,s),o=t.slice(s+1);return n.filters.add(r),`${toValidAssetId(r,"filter")}(${e}${")"!==o?","+o:o}`}}const seen=new WeakSet,transformMemo=(e,t)=>{if(1===e.type){const n=findDir(e,"memo");if(!n||seen.has(e))return;return seen.add(e),()=>{const s=e.codegenNode||t.currentNode.codegenNode;s&&13===s.type&&(1!==e.tagType&&convertToBlock(s,t),e.codegenNode=createCallExpression(t.helper(WITH_MEMO),[n.exp,createFunctionExpression(void 0,s),"_cache",String(t.cached.length)]),t.cached.push(null))}}};function getBaseTransformPreset(e){return[[transformOnce,transformIf,transformMemo,transformFor,transformFilter,transformSlotOutlet,transformElement,trackSlotScopes,transformText],{on:transformOn$1,bind:transformBind,model:transformModel$1}]}function baseCompile(e,t={}){const n=t.onError||defaultOnError,s="module"===t.mode;!0===t.prefixIdentifiers?n(createCompilerError(47)):s&&n(createCompilerError(48));t.cacheHandlers&&n(createCompilerError(49)),t.scopeId&&!s&&n(createCompilerError(50));const r=extend({},t,{prefixIdentifiers:!1}),o=isString(e)?baseParse(e,r):e,[i,a]=getBaseTransformPreset();return transform(o,extend({},r,{nodeTransforms:[...i,...t.nodeTransforms||[]],directiveTransforms:extend({},a,t.directiveTransforms||{})})),generate(o,r)}const noopDirectiveTransform=()=>({props:[]}),V_MODEL_RADIO=Symbol(""),V_MODEL_CHECKBOX=Symbol(""),V_MODEL_TEXT=Symbol(""),V_MODEL_SELECT=Symbol(""),V_MODEL_DYNAMIC=Symbol(""),V_ON_WITH_MODIFIERS=Symbol(""),V_ON_WITH_KEYS=Symbol(""),V_SHOW=Symbol(""),TRANSITION=Symbol(""),TRANSITION_GROUP=Symbol("");
/**
* @vue/compiler-dom v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let decoder;function decodeHtmlBrowser(e,t=!1){return decoder||(decoder=document.createElement("div")),t?(decoder.innerHTML=`<div foo="${e.replace(/"/g,"&quot;")}">`,decoder.children[0].getAttribute("foo")):(decoder.innerHTML=e,decoder.textContent)}registerRuntimeHelpers({[V_MODEL_RADIO]:"vModelRadio",[V_MODEL_CHECKBOX]:"vModelCheckbox",[V_MODEL_TEXT]:"vModelText",[V_MODEL_SELECT]:"vModelSelect",[V_MODEL_DYNAMIC]:"vModelDynamic",[V_ON_WITH_MODIFIERS]:"withModifiers",[V_ON_WITH_KEYS]:"withKeys",[V_SHOW]:"vShow",[TRANSITION]:"Transition",[TRANSITION_GROUP]:"TransitionGroup"});const parserOptions={parseMode:"html",isVoidTag:isVoidTag,isNativeTag:e=>isHTMLTag(e)||isSVGTag(e)||isMathMLTag(e),isPreTag:e=>"pre"===e,isIgnoreNewlineTag:e=>"pre"===e||"textarea"===e,decodeEntities:decodeHtmlBrowser,isBuiltInComponent:e=>"Transition"===e||"transition"===e?TRANSITION:"TransitionGroup"===e||"transition-group"===e?TRANSITION_GROUP:void 0,getNamespace(e,t,n){let s=t?t.ns:n;if(t&&2===s)if("annotation-xml"===t.tag){if("svg"===e)return 1;t.props.some(e=>6===e.type&&"encoding"===e.name&&null!=e.value&&("text/html"===e.value.content||"application/xhtml+xml"===e.value.content))&&(s=0)}else/^m(?:[ions]|text)$/.test(t.tag)&&"mglyph"!==e&&"malignmark"!==e&&(s=0);else t&&1===s&&("foreignObject"!==t.tag&&"desc"!==t.tag&&"title"!==t.tag||(s=0));if(0===s){if("svg"===e)return 1;if("math"===e)return 2}return s}},transformStyle=e=>{1===e.type&&e.props.forEach((t,n)=>{6===t.type&&"style"===t.name&&t.value&&(e.props[n]={type:7,name:"bind",arg:createSimpleExpression("style",!0,t.loc),exp:parseInlineCSS(t.value.content,t.loc),modifiers:[],loc:t.loc})})},parseInlineCSS=(e,t)=>{const n=parseStringStyle(e);return createSimpleExpression(JSON.stringify(n),!1,t,3)};function createDOMCompilerError(e,t){return createCompilerError(e,t)}const transformVHtml=(e,t,n)=>{const{exp:s,loc:r}=e;return s||n.onError(createDOMCompilerError(53,r)),t.children.length&&(n.onError(createDOMCompilerError(54,r)),t.children.length=0),{props:[createObjectProperty(createSimpleExpression("innerHTML",!0,r),s||createSimpleExpression("",!0))]}},transformVText=(e,t,n)=>{const{exp:s,loc:r}=e;return s||n.onError(createDOMCompilerError(55,r)),t.children.length&&(n.onError(createDOMCompilerError(56,r)),t.children.length=0),{props:[createObjectProperty(createSimpleExpression("textContent",!0),s?getConstantType(s,n)>0?s:createCallExpression(n.helperString(TO_DISPLAY_STRING),[s],r):createSimpleExpression("",!0))]}},transformModel=(e,t,n)=>{const s=transformModel$1(e,t,n);if(!s.props.length||1===t.tagType)return s;e.arg&&n.onError(createDOMCompilerError(58,e.arg.loc));const{tag:r}=t,o=n.isCustomElement(r);if("input"===r||"textarea"===r||"select"===r||o){let i=V_MODEL_TEXT,a=!1;if("input"===r||o){const s=findProp(t,"type");if(s){if(7===s.type)i=V_MODEL_DYNAMIC;else if(s.value)switch(s.value.content){case"radio":i=V_MODEL_RADIO;break;case"checkbox":i=V_MODEL_CHECKBOX;break;case"file":a=!0,n.onError(createDOMCompilerError(59,e.loc))}}else hasDynamicKeyVBind(t)&&(i=V_MODEL_DYNAMIC)}else"select"===r&&(i=V_MODEL_SELECT);a||(s.needRuntime=n.helper(i))}else n.onError(createDOMCompilerError(57,e.loc));return s.props=s.props.filter(e=>!(4===e.key.type&&"modelValue"===e.key.content)),s},isEventOptionModifier=makeMap("passive,once,capture"),isNonKeyModifier=makeMap("stop,prevent,self,ctrl,shift,alt,meta,exact,middle"),maybeKeyModifier=makeMap("left,right"),isKeyboardEvent=makeMap("onkeyup,onkeydown,onkeypress"),resolveModifiers=(e,t,n,s)=>{const r=[],o=[],i=[];for(let a=0;a<t.length;a++){const s=t[a].content;"native"===s&&checkCompatEnabled("COMPILER_V_ON_NATIVE",n)||isEventOptionModifier(s)?i.push(s):maybeKeyModifier(s)?isStaticExp(e)?isKeyboardEvent(e.content.toLowerCase())?r.push(s):o.push(s):(r.push(s),o.push(s)):isNonKeyModifier(s)?o.push(s):r.push(s)}return{keyModifiers:r,nonKeyModifiers:o,eventOptionModifiers:i}},transformClick=(e,t)=>isStaticExp(e)&&"onclick"===e.content.toLowerCase()?createSimpleExpression(t,!0):4!==e.type?createCompoundExpression(["(",e,`) === "onClick" ? "${t}" : (`,e,")"]):e,transformOn=(e,t,n)=>transformOn$1(e,t,n,t=>{const{modifiers:s}=e;if(!s.length)return t;let{key:r,value:o}=t.props[0];const{keyModifiers:i,nonKeyModifiers:a,eventOptionModifiers:c}=resolveModifiers(r,s,n,e.loc);if(a.includes("right")&&(r=transformClick(r,"onContextmenu")),a.includes("middle")&&(r=transformClick(r,"onMouseup")),a.length&&(o=createCallExpression(n.helper(V_ON_WITH_MODIFIERS),[o,JSON.stringify(a)])),!i.length||isStaticExp(r)&&!isKeyboardEvent(r.content.toLowerCase())||(o=createCallExpression(n.helper(V_ON_WITH_KEYS),[o,JSON.stringify(i)])),c.length){const e=c.map(capitalize).join("");r=isStaticExp(r)?createSimpleExpression(`${r.content}${e}`,!0):createCompoundExpression(["(",r,`) + "${e}"`])}return{props:[createObjectProperty(r,o)]}}),transformShow=(e,t,n)=>{const{exp:s,loc:r}=e;return s||n.onError(createDOMCompilerError(61,r)),{props:[],needRuntime:n.helper(V_SHOW)}},ignoreSideEffectTags=(e,t)=>{1!==e.type||0!==e.tagType||"script"!==e.tag&&"style"!==e.tag||t.removeNode()},DOMNodeTransforms=[transformStyle],DOMDirectiveTransforms={cloak:noopDirectiveTransform,html:transformVHtml,text:transformVText,model:transformModel,on:transformOn,show:transformShow};function compile(e,t={}){return baseCompile(e,extend({},parserOptions,t,{nodeTransforms:[ignoreSideEffectTags,...DOMNodeTransforms,...t.nodeTransforms||[]],directiveTransforms:extend({},DOMDirectiveTransforms,t.directiveTransforms||{}),transformHoist:null}))}
/**
* vue v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const compileCache=Object.create(null);function compileToFunction(e,t){if(!isString(e)){if(!e.nodeType)return NOOP;e=e.innerHTML}const n=genCacheKey(e,t),s=compileCache[n];if(s)return s;if("#"===e[0]){const t=document.querySelector(e);e=t?t.innerHTML:""}const r=extend({hoistStatic:!0,onError:void 0,onWarn:NOOP},t);r.isCustomElement||"undefined"==typeof customElements||(r.isCustomElement=e=>!!customElements.get(e));const{code:o}=compile(e,r),i=new Function("Vue",o)(runtimeDom);return i._rc=!0,compileCache[n]=i}registerRuntimeCompiler(compileToFunction);var commonjsGlobal="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function getDefaultExportFromCjs(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var md5$1={exports:{}};
/**
 * [js-md5]{@link https://github.com/emn178/js-md5}
 *
 * @namespace md5
 * @version 0.7.3
 * <AUTHOR> Yi-Cyuan [<EMAIL>]
 * @copyright Chen, Yi-Cyuan 2014-2017
 * @license MIT
 */(function(module){(function(){var ERROR="input is invalid type",WINDOW="object"==typeof window,root=WINDOW?window:{};root.JS_MD5_NO_WINDOW&&(WINDOW=!1);var WEB_WORKER=!WINDOW&&"object"==typeof self,NODE_JS=!root.JS_MD5_NO_NODE_JS&&"object"==typeof process&&process.versions&&process.versions.node;NODE_JS?root=commonjsGlobal:WEB_WORKER&&(root=self);var COMMON_JS=!root.JS_MD5_NO_COMMON_JS&&module.exports,ARRAY_BUFFER=!root.JS_MD5_NO_ARRAY_BUFFER&&"undefined"!=typeof ArrayBuffer,HEX_CHARS="0123456789abcdef".split(""),EXTRA=[128,32768,8388608,-**********],SHIFT=[0,8,16,24],OUTPUT_TYPES=["hex","array","digest","buffer","arrayBuffer","base64"],BASE64_ENCODE_CHAR="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),blocks=[],buffer8;if(ARRAY_BUFFER){var buffer=new ArrayBuffer(68);buffer8=new Uint8Array(buffer),blocks=new Uint32Array(buffer)}!root.JS_MD5_NO_NODE_JS&&Array.isArray||(Array.isArray=function(e){return"[object Array]"===Object.prototype.toString.call(e)}),!ARRAY_BUFFER||!root.JS_MD5_NO_ARRAY_BUFFER_IS_VIEW&&ArrayBuffer.isView||(ArrayBuffer.isView=function(e){return"object"==typeof e&&e.buffer&&e.buffer.constructor===ArrayBuffer});var createOutputMethod=function(e){return function(t){return new Md5(!0).update(t)[e]()}},createMethod=function(){var e=createOutputMethod("hex");NODE_JS&&(e=nodeWrap(e)),e.create=function(){return new Md5},e.update=function(t){return e.create().update(t)};for(var t=0;t<OUTPUT_TYPES.length;++t){var n=OUTPUT_TYPES[t];e[n]=createOutputMethod(n)}return e},nodeWrap=function(method){var crypto=eval("require('crypto')"),Buffer=eval("require('buffer').Buffer"),nodeMethod=function(e){if("string"==typeof e)return crypto.createHash("md5").update(e,"utf8").digest("hex");if(null==e)throw ERROR;return e.constructor===ArrayBuffer&&(e=new Uint8Array(e)),Array.isArray(e)||ArrayBuffer.isView(e)||e.constructor===Buffer?crypto.createHash("md5").update(new Buffer(e)).digest("hex"):method(e)};return nodeMethod};function Md5(e){if(e)blocks[0]=blocks[16]=blocks[1]=blocks[2]=blocks[3]=blocks[4]=blocks[5]=blocks[6]=blocks[7]=blocks[8]=blocks[9]=blocks[10]=blocks[11]=blocks[12]=blocks[13]=blocks[14]=blocks[15]=0,this.blocks=blocks,this.buffer8=buffer8;else if(ARRAY_BUFFER){var t=new ArrayBuffer(68);this.buffer8=new Uint8Array(t),this.blocks=new Uint32Array(t)}else this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];this.h0=this.h1=this.h2=this.h3=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}Md5.prototype.update=function(e){if(!this.finalized){var t,n=typeof e;if("string"!==n){if("object"!==n)throw ERROR;if(null===e)throw ERROR;if(ARRAY_BUFFER&&e.constructor===ArrayBuffer)e=new Uint8Array(e);else if(!(Array.isArray(e)||ARRAY_BUFFER&&ArrayBuffer.isView(e)))throw ERROR;t=!0}for(var s,r,o=0,i=e.length,a=this.blocks,c=this.buffer8;o<i;){if(this.hashed&&(this.hashed=!1,a[0]=a[16],a[16]=a[1]=a[2]=a[3]=a[4]=a[5]=a[6]=a[7]=a[8]=a[9]=a[10]=a[11]=a[12]=a[13]=a[14]=a[15]=0),t)if(ARRAY_BUFFER)for(r=this.start;o<i&&r<64;++o)c[r++]=e[o];else for(r=this.start;o<i&&r<64;++o)a[r>>2]|=e[o]<<SHIFT[3&r++];else if(ARRAY_BUFFER)for(r=this.start;o<i&&r<64;++o)(s=e.charCodeAt(o))<128?c[r++]=s:s<2048?(c[r++]=192|s>>6,c[r++]=128|63&s):s<55296||s>=57344?(c[r++]=224|s>>12,c[r++]=128|s>>6&63,c[r++]=128|63&s):(s=65536+((1023&s)<<10|1023&e.charCodeAt(++o)),c[r++]=240|s>>18,c[r++]=128|s>>12&63,c[r++]=128|s>>6&63,c[r++]=128|63&s);else for(r=this.start;o<i&&r<64;++o)(s=e.charCodeAt(o))<128?a[r>>2]|=s<<SHIFT[3&r++]:s<2048?(a[r>>2]|=(192|s>>6)<<SHIFT[3&r++],a[r>>2]|=(128|63&s)<<SHIFT[3&r++]):s<55296||s>=57344?(a[r>>2]|=(224|s>>12)<<SHIFT[3&r++],a[r>>2]|=(128|s>>6&63)<<SHIFT[3&r++],a[r>>2]|=(128|63&s)<<SHIFT[3&r++]):(s=65536+((1023&s)<<10|1023&e.charCodeAt(++o)),a[r>>2]|=(240|s>>18)<<SHIFT[3&r++],a[r>>2]|=(128|s>>12&63)<<SHIFT[3&r++],a[r>>2]|=(128|s>>6&63)<<SHIFT[3&r++],a[r>>2]|=(128|63&s)<<SHIFT[3&r++]);this.lastByteIndex=r,this.bytes+=r-this.start,r>=64?(this.start=r-64,this.hash(),this.hashed=!0):this.start=r}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296|0,this.bytes=this.bytes%4294967296),this}},Md5.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var e=this.blocks,t=this.lastByteIndex;e[t>>2]|=EXTRA[3&t],t>=56&&(this.hashed||this.hash(),e[0]=e[16],e[16]=e[1]=e[2]=e[3]=e[4]=e[5]=e[6]=e[7]=e[8]=e[9]=e[10]=e[11]=e[12]=e[13]=e[14]=e[15]=0),e[14]=this.bytes<<3,e[15]=this.hBytes<<3|this.bytes>>>29,this.hash()}},Md5.prototype.hash=function(){var e,t,n,s,r,o,i=this.blocks;this.first?t=((t=((e=((e=i[0]-680876937)<<7|e>>>25)-271733879|0)^(n=((n=(-271733879^(s=((s=(-1732584194^2004318071&e)+i[1]-117830708)<<12|s>>>20)+e|0)&(-271733879^e))+i[2]-1126478375)<<17|n>>>15)+s|0)&(s^e))+i[3]-1316259209)<<22|t>>>10)+n|0:(e=this.h0,t=this.h1,n=this.h2,t=((t+=((e=((e+=((s=this.h3)^t&(n^s))+i[0]-680876936)<<7|e>>>25)+t|0)^(n=((n+=(t^(s=((s+=(n^e&(t^n))+i[1]-389564586)<<12|s>>>20)+e|0)&(e^t))+i[2]+606105819)<<17|n>>>15)+s|0)&(s^e))+i[3]-1044525330)<<22|t>>>10)+n|0),t=((t+=((e=((e+=(s^t&(n^s))+i[4]-176418897)<<7|e>>>25)+t|0)^(n=((n+=(t^(s=((s+=(n^e&(t^n))+i[5]+1200080426)<<12|s>>>20)+e|0)&(e^t))+i[6]-1473231341)<<17|n>>>15)+s|0)&(s^e))+i[7]-45705983)<<22|t>>>10)+n|0,t=((t+=((e=((e+=(s^t&(n^s))+i[8]+1770035416)<<7|e>>>25)+t|0)^(n=((n+=(t^(s=((s+=(n^e&(t^n))+i[9]-1958414417)<<12|s>>>20)+e|0)&(e^t))+i[10]-42063)<<17|n>>>15)+s|0)&(s^e))+i[11]-1990404162)<<22|t>>>10)+n|0,t=((t+=((e=((e+=(s^t&(n^s))+i[12]+1804603682)<<7|e>>>25)+t|0)^(n=((n+=(t^(s=((s+=(n^e&(t^n))+i[13]-40341101)<<12|s>>>20)+e|0)&(e^t))+i[14]-1502002290)<<17|n>>>15)+s|0)&(s^e))+i[15]+1236535329)<<22|t>>>10)+n|0,t=((t+=((s=((s+=(t^n&((e=((e+=(n^s&(t^n))+i[1]-165796510)<<5|e>>>27)+t|0)^t))+i[6]-1069501632)<<9|s>>>23)+e|0)^e&((n=((n+=(e^t&(s^e))+i[11]+643717713)<<14|n>>>18)+s|0)^s))+i[0]-373897302)<<20|t>>>12)+n|0,t=((t+=((s=((s+=(t^n&((e=((e+=(n^s&(t^n))+i[5]-701558691)<<5|e>>>27)+t|0)^t))+i[10]+38016083)<<9|s>>>23)+e|0)^e&((n=((n+=(e^t&(s^e))+i[15]-660478335)<<14|n>>>18)+s|0)^s))+i[4]-405537848)<<20|t>>>12)+n|0,t=((t+=((s=((s+=(t^n&((e=((e+=(n^s&(t^n))+i[9]+568446438)<<5|e>>>27)+t|0)^t))+i[14]-1019803690)<<9|s>>>23)+e|0)^e&((n=((n+=(e^t&(s^e))+i[3]-187363961)<<14|n>>>18)+s|0)^s))+i[8]+1163531501)<<20|t>>>12)+n|0,t=((t+=((s=((s+=(t^n&((e=((e+=(n^s&(t^n))+i[13]-1444681467)<<5|e>>>27)+t|0)^t))+i[2]-51403784)<<9|s>>>23)+e|0)^e&((n=((n+=(e^t&(s^e))+i[7]+1735328473)<<14|n>>>18)+s|0)^s))+i[12]-1926607734)<<20|t>>>12)+n|0,t=((t+=((o=(s=((s+=((r=t^n)^(e=((e+=(r^s)+i[5]-378558)<<4|e>>>28)+t|0))+i[8]-2022574463)<<11|s>>>21)+e|0)^e)^(n=((n+=(o^t)+i[11]+1839030562)<<16|n>>>16)+s|0))+i[14]-35309556)<<23|t>>>9)+n|0,t=((t+=((o=(s=((s+=((r=t^n)^(e=((e+=(r^s)+i[1]-1530992060)<<4|e>>>28)+t|0))+i[4]+1272893353)<<11|s>>>21)+e|0)^e)^(n=((n+=(o^t)+i[7]-155497632)<<16|n>>>16)+s|0))+i[10]-1094730640)<<23|t>>>9)+n|0,t=((t+=((o=(s=((s+=((r=t^n)^(e=((e+=(r^s)+i[13]+681279174)<<4|e>>>28)+t|0))+i[0]-358537222)<<11|s>>>21)+e|0)^e)^(n=((n+=(o^t)+i[3]-722521979)<<16|n>>>16)+s|0))+i[6]+76029189)<<23|t>>>9)+n|0,t=((t+=((o=(s=((s+=((r=t^n)^(e=((e+=(r^s)+i[9]-640364487)<<4|e>>>28)+t|0))+i[12]-421815835)<<11|s>>>21)+e|0)^e)^(n=((n+=(o^t)+i[15]+530742520)<<16|n>>>16)+s|0))+i[2]-995338651)<<23|t>>>9)+n|0,t=((t+=((s=((s+=(t^((e=((e+=(n^(t|~s))+i[0]-198630844)<<6|e>>>26)+t|0)|~n))+i[7]+1126891415)<<10|s>>>22)+e|0)^((n=((n+=(e^(s|~t))+i[14]-1416354905)<<15|n>>>17)+s|0)|~e))+i[5]-57434055)<<21|t>>>11)+n|0,t=((t+=((s=((s+=(t^((e=((e+=(n^(t|~s))+i[12]+1700485571)<<6|e>>>26)+t|0)|~n))+i[3]-1894986606)<<10|s>>>22)+e|0)^((n=((n+=(e^(s|~t))+i[10]-1051523)<<15|n>>>17)+s|0)|~e))+i[1]-2054922799)<<21|t>>>11)+n|0,t=((t+=((s=((s+=(t^((e=((e+=(n^(t|~s))+i[8]+1873313359)<<6|e>>>26)+t|0)|~n))+i[15]-30611744)<<10|s>>>22)+e|0)^((n=((n+=(e^(s|~t))+i[6]-1560198380)<<15|n>>>17)+s|0)|~e))+i[13]+1309151649)<<21|t>>>11)+n|0,t=((t+=((s=((s+=(t^((e=((e+=(n^(t|~s))+i[4]-145523070)<<6|e>>>26)+t|0)|~n))+i[11]-1120210379)<<10|s>>>22)+e|0)^((n=((n+=(e^(s|~t))+i[2]+718787259)<<15|n>>>17)+s|0)|~e))+i[9]-343485551)<<21|t>>>11)+n|0,this.first?(this.h0=e+1732584193|0,this.h1=t-271733879|0,this.h2=n-1732584194|0,this.h3=s+271733878|0,this.first=!1):(this.h0=this.h0+e|0,this.h1=this.h1+t|0,this.h2=this.h2+n|0,this.h3=this.h3+s|0)},Md5.prototype.hex=function(){this.finalize();var e=this.h0,t=this.h1,n=this.h2,s=this.h3;return HEX_CHARS[e>>4&15]+HEX_CHARS[15&e]+HEX_CHARS[e>>12&15]+HEX_CHARS[e>>8&15]+HEX_CHARS[e>>20&15]+HEX_CHARS[e>>16&15]+HEX_CHARS[e>>28&15]+HEX_CHARS[e>>24&15]+HEX_CHARS[t>>4&15]+HEX_CHARS[15&t]+HEX_CHARS[t>>12&15]+HEX_CHARS[t>>8&15]+HEX_CHARS[t>>20&15]+HEX_CHARS[t>>16&15]+HEX_CHARS[t>>28&15]+HEX_CHARS[t>>24&15]+HEX_CHARS[n>>4&15]+HEX_CHARS[15&n]+HEX_CHARS[n>>12&15]+HEX_CHARS[n>>8&15]+HEX_CHARS[n>>20&15]+HEX_CHARS[n>>16&15]+HEX_CHARS[n>>28&15]+HEX_CHARS[n>>24&15]+HEX_CHARS[s>>4&15]+HEX_CHARS[15&s]+HEX_CHARS[s>>12&15]+HEX_CHARS[s>>8&15]+HEX_CHARS[s>>20&15]+HEX_CHARS[s>>16&15]+HEX_CHARS[s>>28&15]+HEX_CHARS[s>>24&15]},Md5.prototype.toString=Md5.prototype.hex,Md5.prototype.digest=function(){this.finalize();var e=this.h0,t=this.h1,n=this.h2,s=this.h3;return[255&e,e>>8&255,e>>16&255,e>>24&255,255&t,t>>8&255,t>>16&255,t>>24&255,255&n,n>>8&255,n>>16&255,n>>24&255,255&s,s>>8&255,s>>16&255,s>>24&255]},Md5.prototype.array=Md5.prototype.digest,Md5.prototype.arrayBuffer=function(){this.finalize();var e=new ArrayBuffer(16),t=new Uint32Array(e);return t[0]=this.h0,t[1]=this.h1,t[2]=this.h2,t[3]=this.h3,e},Md5.prototype.buffer=Md5.prototype.arrayBuffer,Md5.prototype.base64=function(){for(var e,t,n,s="",r=this.array(),o=0;o<15;)e=r[o++],t=r[o++],n=r[o++],s+=BASE64_ENCODE_CHAR[e>>>2]+BASE64_ENCODE_CHAR[63&(e<<4|t>>>4)]+BASE64_ENCODE_CHAR[63&(t<<2|n>>>6)]+BASE64_ENCODE_CHAR[63&n];return e=r[o],s+=BASE64_ENCODE_CHAR[e>>>2]+BASE64_ENCODE_CHAR[e<<4&63]+"=="};var exports=createMethod();COMMON_JS?module.exports=exports:root.md5=exports})()})(md5$1);var md5Exports=md5$1.exports;const md5=getDefaultExportFromCjs(md5Exports),_export_sfc=(e,t)=>{const n=e.__vccOpts||e;for(const[s,r]of t)n[s]=r;return n},_sfc_main={name:"RainbowShopSimple",data:()=>({showModal:!1,showAnnounceModal:!1,showCustomerServiceModal:!1,showVersionModal:!1,activeTab:"shop",searchKeyword:"",selectedCategoryL1:"",selectedCategoryL2:"",selectedProduct:"",productPrice:"",stockCount:"",orderQuantity:1,showAlert:!1,alertMessage:"",showCategoryL2:!1,showProductSelect:!1,searchQuery:"",showQueryResult:!1,queryResults:[{id:"ORD20250731001",account:"<EMAIL>",productName:"QQ会员充值",quantity:1,createTime:"2025-07-31 14:30:25",status:"completed",statusText:"已完成",amount:"15.00"}],backgroundImageUrl:"https://imgapi.xl0408.top/index.php",currentTime:"",tipStyle:"default",tipAnimation:"tada",showTip:!0,timeTimer:null,userId:"",userKey:"",token:"",categoriesData:[],productsData:[],currentSubCategories:[]}),computed:{getSubCategoriesByParentId(){if(!this.selectedCategoryL1||!this.categoriesData.length)return[];const e=this.categoriesData.find(e=>e.id==this.selectedCategoryL1);return e&&e.children||[]}},methods:{loadInitialData(){return __async(this,null,function*(){try{this.showAlert=!0,this.alertMessage="正在加载分类数据...";(yield this.getCategoriesData())?(this.showAlert=!1,console.log("分类数据加载成功")):(this.showAlert=!0,this.alertMessage="分类数据加载失败，请刷新页面重试")}catch(e){console.error("初始化数据加载失败:",e),this.showAlert=!0,this.alertMessage="初始化失败，请刷新页面重试"}})},increaseQuantity(){this.orderQuantity++},decreaseQuantity(){this.orderQuantity>1&&this.orderQuantity--},buyNow(){alert("购买功能已简化，这是演示版本")},submitQuery(){this.showQueryResult=!0,this.searchQuery.trim()?"ORD20250731001"===this.searchQuery?this.queryResults=[{id:"ORD20250731001",account:"<EMAIL>",productName:"QQ会员充值",quantity:1,createTime:"2025-07-31 14:30:25",status:"completed",statusText:"已完成",amount:"15.00"}]:this.queryResults=[]:this.queryResults=[{id:"ORD20250731001",account:"<EMAIL>",productName:"QQ会员充值",quantity:1,createTime:"2025-07-31 14:30:25",status:"completed",statusText:"已完成",amount:"15.00"}]},updateCurrentTime(){const e=new Date,t=e.getFullYear(),n=String(e.getMonth()+1).padStart(2,"0"),s=String(e.getDate()).padStart(2,"0"),r=String(e.getHours()).padStart(2,"0"),o=String(e.getMinutes()).padStart(2,"0"),i=String(e.getSeconds()).padStart(2,"0");this.currentTime=`［${t}年${n}月${s}日 ${r}:${o}:${i}］当前系统时间`},startTimeTimer(){this.updateCurrentTime(),this.timeTimer=setInterval(()=>{this.updateCurrentTime()},1e3)},stopTimeTimer(){this.timeTimer&&(clearInterval(this.timeTimer),this.timeTimer=null)},changeTipStyle(){const e=["default","success","warning","info"],t=e.indexOf(this.tipStyle);this.tipStyle=e[(t+1)%e.length]},changeTipAnimation(){const e=["tada","bounce","pulse","flash"],t=e.indexOf(this.tipAnimation);this.tipAnimation=e[(t+1)%e.length]},toggleTip(){this.showTip=!this.showTip},onTipClick(){this.changeTipStyle();const e=document.querySelector(".shuaibi-tip");e&&(e.classList.remove("animated",this.tipAnimation),setTimeout(()=>{e.classList.add("animated",this.tipAnimation)},10))},generateSignature(e,t){if(!e||!t)return console.error("generateSignature: userId或userKey为空"),"";try{return md5(e+t)}catch(n){return console.error("MD5签名生成失败:",n),""}},getUserInfo(){return __async(this,null,function*(){try{const e=localStorage.getItem("userId"),t=localStorage.getItem("userKey"),n=localStorage.getItem("token");if(e&&t&&n)return this.userId=e,this.userKey=t,this.token=n,!0;const s=yield fetch("/user/api/GetUser/",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"}});if(!s.ok)throw new Error(`HTTP请求错误! 状态: ${s.status}`);const r=yield s.json(),o=s.headers.get("token");if(r&&r.user&&o)return this.userId=r.user.id.toString(),this.userKey=r.user.user_key,this.token=o,localStorage.setItem("userId",this.userId),localStorage.setItem("userKey",this.userKey),localStorage.setItem("token",this.token),!0;throw new Error("获取用户信息失败")}catch(e){return console.error("获取用户信息失败:",e),this.showAlert=!0,this.alertMessage="获取用户信息失败，请刷新页面重试",!1}})},getCategoriesData(){return __async(this,null,function*(){try{if(!(yield this.getUserInfo()))return!1;const e=this.generateSignature(this.userId,this.userKey),t=new FormData;t.append("userId",this.userId),t.append("sign",e);const n=yield fetch("/user/api/categories/",{method:"POST",body:t,headers:{Token:this.token}});if(!n.ok)throw new Error(`HTTP请求错误! 状态: ${n.status}`);const s=yield n.json();if(200===s.code&&s.data)return this.categoriesData=s.data,console.log("分类数据获取成功:",this.categoriesData),!0;throw new Error(s.message||"获取分类数据失败")}catch(e){return console.error("获取分类数据失败:",e),this.showAlert=!0,this.alertMessage="获取分类数据失败，请刷新页面重试",!1}})},getProductListData(e,t=!0){return __async(this,null,function*(){try{if(!(yield this.getUserInfo()))return[];let n=[],s=1,r=!0;for(;r;){const o=e+this.userId+this.userKey,i=md5(o),a=new FormData;a.append("categoryId",e),a.append("userId",this.userId),a.append("sign",i),a.append("page",s),a.append("page_size",100);const c=yield fetch("/user/api/productlist/",{method:"POST",body:a,headers:{Token:this.token}});if(!c.ok)throw new Error(`HTTP请求错误! 状态: ${c.status}`);const l=yield c.json();if(200!==l.code||!l.data||!l.data.items)throw new Error(l.msg||"获取商品列表失败");n=n.concat(l.data.items),t&&l.data.pagination&&l.data.pagination.has_next?s++:r=!1}return console.log(`获取到 ${n.length} 个商品`),n}catch(n){return console.error("获取商品列表失败:",n),this.showAlert=!0,this.alertMessage="获取商品列表失败，请重试",[]}})},onCategoryL1Change(){return __async(this,null,function*(){if(console.log("一级分类选择变化:",this.selectedCategoryL1),this.selectedCategoryL2="",this.selectedProduct="",this.productsData=[],this.showProductSelect=!1,this.selectedCategoryL1){const e=this.categoriesData.find(e=>e.id==this.selectedCategoryL1);e&&e.children&&e.children.length>0?(this.currentSubCategories=e.children,this.showCategoryL2=!0):(this.showCategoryL2=!1,this.currentSubCategories=[])}else this.showCategoryL2=!1,this.currentSubCategories=[]})},onCategoryL2Change(){return __async(this,null,function*(){if(console.log("二级分类选择变化:",this.selectedCategoryL2),this.selectedProduct="",this.productsData=[],this.selectedCategoryL2){this.showAlert=!0,this.alertMessage="正在加载商品列表...";const e=yield this.getProductListData(this.selectedCategoryL2,!0);e&&e.length>0?(this.productsData=e,this.showProductSelect=!0,this.showAlert=!1,console.log(`成功加载 ${e.length} 个商品`)):(this.showProductSelect=!1,this.showAlert=!0,this.alertMessage="该分类下暂无商品")}else this.showProductSelect=!1})}},watch:{selectedCategoryL1(e){e?(this.showCategoryL2=!0,this.selectedCategoryL2="",this.selectedProduct="",this.showProductSelect=!1):(this.showCategoryL2=!1,this.showProductSelect=!1)},selectedCategoryL2(e){e?(this.showProductSelect=!0,this.selectedProduct=""):this.showProductSelect=!1}},mounted(){return __async(this,null,function*(){console.log("依思商城已加载"),this.startTimeTimer(),yield this.loadInitialData()})},beforeDestroy(){this.stopTimeTimer()}},_hoisted_1={id:"app"},_hoisted_2={class:"main-container"},_hoisted_3={class:"col-xs-12 col-sm-10 col-md-8 col-lg-5 center-block",style:{float:"none"}},_hoisted_4={class:"modal fade",align:"left",id:"myModal",tabindex:"-1",role:"dialog","aria-labelledby":"myModalLabel","aria-hidden":"true"},_hoisted_5={class:"modal-dialog"},_hoisted_6={class:"modal-content"},_hoisted_7={class:"modal-header-tabs"},_hoisted_8={class:"modal-footer"},_hoisted_9={class:"modal fade",align:"left",id:"anounce",tabindex:"-1",role:"dialog","aria-labelledby":"myModalLabel","aria-hidden":"true",style:{display:"none"}},_hoisted_10={class:"modal-dialog"},_hoisted_11={class:"modal-content"},_hoisted_12={class:"modal-header",style:{background:"linear-gradient(120deg, #31B404 0%, #D7DF01 100%)"}},_hoisted_13={class:"modal-footer"},_hoisted_14={class:"widget"},_hoisted_15={class:"widget-content text-center action-buttons"},_hoisted_16={class:"btn-group btn-group-justified modern-btn-group"},_hoisted_17={class:"btn-group"},_hoisted_18={class:"btn-group"},_hoisted_19={class:"block full2 modern-tabs"},_hoisted_20={class:"block-title"},_hoisted_21={class:"nav nav-tabs modern-nav-tabs","data-toggle":"tabs"},_hoisted_22={class:"tab-content"},_hoisted_23={class:"text-center"},_hoisted_24={id:"goodTypeContents",class:"modern-order-form"},_hoisted_25={class:"form-group",id:"display_searchBar"},_hoisted_26={class:"input-group"},_hoisted_27={class:"form-group",id:"display_price",style:{display:"none","text-align":"center",color:"#4169E1","font-weight":"bold"}},_hoisted_28={class:"input-group"},_hoisted_29={class:"form-group",id:"display_left",style:{display:"none"}},_hoisted_30={class:"input-group"},_hoisted_31={class:"form-group",id:"display_num",style:{display:"none"}},_hoisted_32={class:"input-group"},_hoisted_33={class:"input-group-btn"},_hoisted_34={class:"input-group-btn"},_hoisted_35=["innerHTML"],_hoisted_36={class:"form-group"},_hoisted_37={class:"input-group"},_hoisted_38=["value"],_hoisted_39={class:"form-group"},_hoisted_40={class:"input-group"},_hoisted_41=["value"],_hoisted_42={class:"form-group"},_hoisted_43={class:"input-group"},_hoisted_44=["value"],_hoisted_45={class:"form-group"},_hoisted_46={class:"customer-service-card"},_hoisted_47={class:"service-action"},_hoisted_48={class:"search-container"},_hoisted_49={class:"search-input-wrapper"},_hoisted_50={class:"query-results"},_hoisted_51={key:0,class:"order-cards"},_hoisted_52={class:"order-header"},_hoisted_53={class:"order-id"},_hoisted_54={class:"order-content"},_hoisted_55={class:"order-info"},_hoisted_56={class:"info-item"},_hoisted_57={class:"info-item"},_hoisted_58={class:"info-row"},_hoisted_59={class:"info-item"},_hoisted_60={class:"info-item"},_hoisted_61={class:"amount"},_hoisted_62={class:"info-item"},_hoisted_63={key:1,class:"empty-state"},_hoisted_64={class:"table table-borderless table-pricing"},_hoisted_65={class:"active"},_hoisted_66={class:"modal fade col-xs-12",align:"left",id:"lxkf",tabindex:"-1",role:"dialog","aria-labelledby":"myModalLabel","aria-hidden":"true"},_hoisted_67={class:"modal-dialog panel panel-primary animation-fadeInQuick2"},_hoisted_68={class:"modal-content"},_hoisted_69={class:"list-group-item reed",style:{background:"linear-gradient(120deg, #5ED1D7 10%, #71D7A2 90%)"}},_hoisted_70={class:"modal-footer"},_hoisted_71={class:"modal fade",align:"left",id:"userjs",tabindex:"-1",role:"dialog","aria-labelledby":"myModalLabel","aria-hidden":"true",style:{display:"none"}},_hoisted_72={class:"modal-dialog"},_hoisted_73={class:"modal-content"},_hoisted_74={class:"list-group-item reed",style:{background:"linear-gradient(120deg, #FE2EF7 10%, #71D7A2 90%)"}},_hoisted_75={class:"modal-footer"};function _sfc_render(e,t,n,s,r,o){return openBlock(),createElementBlock("div",_hoisted_1,[createBaseVNode("div",{class:"full-bg",style:normalizeStyle(r.backgroundImageUrl?{backgroundImage:"url("+r.backgroundImageUrl+")"}:{})},null,4),createBaseVNode("div",_hoisted_2,[createBaseVNode("div",_hoisted_3,[withDirectives(createBaseVNode("div",_hoisted_4,[createBaseVNode("div",_hoisted_5,[createBaseVNode("div",_hoisted_6,[createBaseVNode("div",_hoisted_7,[createBaseVNode("button",{type:"button",class:"close",onClick:t[0]||(t[0]=e=>r.showModal=!1)},t[31]||(t[31]=[createBaseVNode("span",{"aria-hidden":"true"},"×",-1),createBaseVNode("span",{class:"sr-only"},"Close",-1)])),t[32]||(t[32]=createBaseVNode("h4",{class:"modal-title",id:"myModalLabel"},"依思商城",-1))]),t[33]||(t[33]=createBaseVNode("div",{class:"modal-body"},null,-1)),createBaseVNode("div",_hoisted_8,[createBaseVNode("button",{type:"button",class:"btn btn-default",onClick:t[1]||(t[1]=e=>r.showModal=!1)},"知道啦")])])])],512),[[vShow,r.showModal]]),withDirectives(createBaseVNode("div",_hoisted_9,[createBaseVNode("div",_hoisted_10,[createBaseVNode("div",_hoisted_11,[createBaseVNode("div",_hoisted_12,[createBaseVNode("button",{type:"button",class:"close",onClick:t[2]||(t[2]=e=>r.showAnnounceModal=!1)},t[34]||(t[34]=[createBaseVNode("span",{"aria-hidden":"true"},"×",-1),createBaseVNode("span",{class:"sr-only"},"Close",-1)])),t[35]||(t[35]=createBaseVNode("div",{class:"text-center"},[createBaseVNode("h4",{class:"modal-title",id:"myModalLabel"},[createBaseVNode("b",null,[createBaseVNode("span",{style:{color:"#fff"}},"依思商城")])])],-1))]),t[36]||(t[36]=createStaticVNode('<div class="widget flat radius-bordered"><div class="widget-header bordered-top bordered-themesecondary"><div class="modal-body"><p><li class="list-group-item"><span class="btn btn-danger btn-xs">1</span> 售后问题可直接联系平台在线QQ客服 </li><li class="list-group-item"><span class="btn btn-success btn-xs">2</span> 下单之前请一定要看完该商品的注意事项再进行下单！ </li><li class="list-group-item"><span class="btn btn-info btn-xs">3</span> 所有业务全部恢复，都可以正常下单，欢迎尝试 </li><li class="list-group-item"><span class="btn btn-warning btn-xs">4</span> 温馨提示：请勿重复下单哦！必须要等待前面任务订单完成才可以下单！ </li><li class="list-group-item"><span class="btn btn-primary btn-xs">5</span><a href="./user/regsite.php">价格贵？不怕，点击0元搭建，在后台超低价下单！</a></li><div class="btn-group btn-group-justified"><a target="_blank" class="btn btn-info" href="http://wpa.qq.com/msgrd?v=3&amp;uin=123456&amp;site=qq&amp;menu=yes"><i class="fa fa-qq"></i> 联系客服 </a><a target="_blank" class="btn btn-warning" href="http://qun.qq.com/join.html"><i class="fa fa-users"></i> 官方Q群 </a><a target="_blank" class="btn btn-danger" href="./"><i class="fa fa-cloud-download"></i> APP下载 </a></div></p></div></div></div>',1)),createBaseVNode("div",_hoisted_13,[createBaseVNode("button",{type:"button",class:"btn btn-default",onClick:t[3]||(t[3]=e=>r.showAnnounceModal=!1)},"我明白了")])])])],512),[[vShow,r.showAnnounceModal]]),createBaseVNode("div",_hoisted_14,[t[40]||(t[40]=createStaticVNode('<div class="widget-content themed-background-flat text-center logo-header"><div class="logo-avatar-container"><div class="logo-avatar"><img src="https://q.qlogo.cn/headimg_dl?dst_uin=107766441&amp;spec=640&amp;img_type=jpg" alt="头像" class="avatar-img"></div></div></div><div class="text-center logo-title"><h2><span class="brand-link"><b>依思商城</b></span></h2><p class="brand-subtitle">专业的数字商品交易平台</p></div>',2)),createBaseVNode("div",_hoisted_15,[createBaseVNode("div",_hoisted_16,[createBaseVNode("div",_hoisted_17,[createBaseVNode("a",{class:"btn btn-modern btn-announce",onClick:t[4]||(t[4]=e=>r.showAnnounceModal=!0)},t[37]||(t[37]=[createBaseVNode("i",{class:"fa fa-bullhorn"},null,-1),createBaseVNode("span",null,"公告",-1)]))]),createBaseVNode("div",_hoisted_18,[createBaseVNode("a",{class:"btn btn-modern btn-service",onClick:t[5]||(t[5]=e=>r.showCustomerServiceModal=!0)},t[38]||(t[38]=[createBaseVNode("i",{class:"fa fa-qq"},null,-1),createBaseVNode("span",null,"客服",-1)]))]),t[39]||(t[39]=createBaseVNode("div",{class:"btn-group"},[createBaseVNode("a",{class:"btn btn-modern btn-login",href:"user/login.php"},[createBaseVNode("i",{class:"fa fa-users"}),createBaseVNode("span",null,"登录")])],-1))])])]),createBaseVNode("div",_hoisted_19,[createBaseVNode("div",_hoisted_20,[createBaseVNode("ul",_hoisted_21,[createBaseVNode("li",{class:normalizeClass(["nav-item",{active:"shop"===r.activeTab}])},[createBaseVNode("a",{href:"#shop",onClick:t[6]||(t[6]=e=>r.activeTab="shop"),class:"nav-link"},t[41]||(t[41]=[createBaseVNode("i",{class:"fa fa-shopping-cart"},null,-1),createBaseVNode("span",null,"下单",-1)]))],2),createBaseVNode("li",{class:normalizeClass(["nav-item",{active:"search"===r.activeTab}])},[createBaseVNode("a",{href:"#search",onClick:t[7]||(t[7]=e=>r.activeTab="search"),class:"nav-link"},t[42]||(t[42]=[createBaseVNode("i",{class:"fa fa-search"},null,-1),createBaseVNode("span",null,"查询",-1)]))],2),createBaseVNode("li",{class:normalizeClass(["nav-item",{active:"substation"===r.activeTab}])},[createBaseVNode("a",{href:"#Substation",onClick:t[8]||(t[8]=e=>r.activeTab="substation"),class:"nav-link nav-link-special"},t[43]||(t[43]=[createBaseVNode("i",{class:"fa fa-location-arrow fa-spin"},null,-1),createBaseVNode("span",null,"分站",-1)]))],2),createBaseVNode("li",{class:normalizeClass(["nav-item",{active:"more"===r.activeTab}])},[createBaseVNode("a",{href:"#more",onClick:t[9]||(t[9]=e=>r.activeTab="more"),class:"nav-link"},t[44]||(t[44]=[createBaseVNode("i",{class:"fa fa-list"},null,-1),createBaseVNode("span",null,"更多",-1)]))],2)])]),createBaseVNode("div",_hoisted_22,[createBaseVNode("div",{class:normalizeClass(["tab-pane",{active:"shop"===r.activeTab}]),id:"shop"},[createBaseVNode("div",_hoisted_23,[withDirectives(createBaseVNode("div",{class:normalizeClass(["shuaibi-tip","animated",r.tipAnimation,"text-center",`tip-${r.tipStyle}`]),onClick:t[10]||(t[10]=(...e)=>o.onTipClick&&o.onTipClick(...e)),style:{cursor:"pointer"},title:"点击切换样式"},[t[45]||(t[45]=createBaseVNode("i",{class:"fa fa-heart text-danger"},null,-1)),createBaseVNode("b",null,toDisplayString(r.currentTime),1)],2),[[vShow,r.showTip]])]),createBaseVNode("div",_hoisted_24,[createBaseVNode("div",_hoisted_25,[createBaseVNode("div",_hoisted_26,[t[46]||(t[46]=createBaseVNode("div",{class:"input-group-addon"},"搜索商品",-1)),withDirectives(createBaseVNode("input",{type:"text",id:"searchkw",class:"form-control",placeholder:"搜索商品","onUpdate:modelValue":t[11]||(t[11]=e=>r.searchKeyword=e)},null,512),[[vModelText,r.searchKeyword]]),t[47]||(t[47]=createBaseVNode("div",{class:"input-group-addon search-icon-addon"},[createBaseVNode("svg",{class:"order-search-icon",viewBox:"0 0 1024 1024",xmlns:"http://www.w3.org/2000/svg","shape-rendering":"geometricPrecision","image-rendering":"optimizeQuality"},[createBaseVNode("path",{d:"M959.266 879.165c0 81.582-81.582 81.582-81.582 81.582l-233.38-233.381c-60.529 43.977-134.777 70.217-215.318 70.217-202.755 0-367.117-164.362-367.117-367.117S226.23 63.349 428.985 63.349s367.117 164.362 367.117 367.117c0 80.541-26.241 154.785-70.217 215.318l233.381 233.381zM428.985 144.931c-157.697 0-285.536 127.838-285.536 285.536s127.838 285.536 285.536 285.536 285.536-127.838 285.536-285.536-127.839-285.536-285.536-285.536z"})])],-1))])]),createBaseVNode("div",_hoisted_27,[createBaseVNode("div",_hoisted_28,[t[48]||(t[48]=createBaseVNode("div",{class:"input-group-addon"},"商品价格",-1)),withDirectives(createBaseVNode("input",{type:"text",name:"need",id:"need",class:"form-control",style:{"text-align":"center",color:"#4169E1","font-weight":"bold"},disabled:"","onUpdate:modelValue":t[12]||(t[12]=e=>r.productPrice=e)},null,512),[[vModelText,r.productPrice]])])]),createBaseVNode("div",_hoisted_29,[createBaseVNode("div",_hoisted_30,[t[49]||(t[49]=createBaseVNode("div",{class:"input-group-addon"},"库存数量",-1)),withDirectives(createBaseVNode("input",{type:"text",name:"leftcount",id:"leftcount",class:"form-control",disabled:"","onUpdate:modelValue":t[13]||(t[13]=e=>r.stockCount=e)},null,512),[[vModelText,r.stockCount]])])]),createBaseVNode("div",_hoisted_31,[createBaseVNode("div",_hoisted_32,[t[50]||(t[50]=createBaseVNode("div",{class:"input-group-addon"},"下单份数",-1)),createBaseVNode("span",_hoisted_33,[createBaseVNode("input",{id:"num_min",type:"button",class:"btn btn-info",style:{"border-radius":"0px"},value:"━",onClick:t[14]||(t[14]=(...e)=>o.decreaseQuantity&&o.decreaseQuantity(...e))})]),withDirectives(createBaseVNode("input",{id:"num",name:"num",class:"form-control",type:"number",min:"1","onUpdate:modelValue":t[15]||(t[15]=e=>r.orderQuantity=e)},null,512),[[vModelText,r.orderQuantity]]),createBaseVNode("span",_hoisted_34,[createBaseVNode("input",{id:"num_add",type:"button",class:"btn btn-info",style:{"border-radius":"0px"},value:"✚",onClick:t[16]||(t[16]=(...e)=>o.increaseQuantity&&o.increaseQuantity(...e))})])])]),t[57]||(t[57]=createBaseVNode("div",{id:"inputsname"},null,-1)),withDirectives(createBaseVNode("div",{id:"alert_frame",class:"alert alert-success animated rubberBand",style:{display:"none",background:"linear-gradient(to right,#71D7A2,#5ED1D7)","font-weight":"bold",color:"white"},innerHTML:r.alertMessage},null,8,_hoisted_35),[[vShow,r.showAlert]]),createBaseVNode("div",_hoisted_36,[createBaseVNode("div",_hoisted_37,[t[52]||(t[52]=createBaseVNode("div",{class:"input-group-addon"},"商品分类",-1)),withDirectives(createBaseVNode("select",{id:"category-level-1",class:"form-control","onUpdate:modelValue":t[17]||(t[17]=e=>r.selectedCategoryL1=e),onChange:t[18]||(t[18]=(...e)=>o.onCategoryL1Change&&o.onCategoryL1Change(...e))},[t[51]||(t[51]=createBaseVNode("option",{value:""},"请选择分类",-1)),(openBlock(!0),createElementBlock(Fragment,null,renderList(r.categoriesData,e=>(openBlock(),createElementBlock("option",{key:e.id,value:e.id},toDisplayString(e.name),9,_hoisted_38))),128))],544),[[vModelSelect,r.selectedCategoryL1]])])]),withDirectives(createBaseVNode("div",_hoisted_39,[createBaseVNode("div",_hoisted_40,[t[54]||(t[54]=createBaseVNode("div",{class:"input-group-addon"},"子分类",-1)),withDirectives(createBaseVNode("select",{id:"category-level-2",class:"form-control","onUpdate:modelValue":t[19]||(t[19]=e=>r.selectedCategoryL2=e),onChange:t[20]||(t[20]=(...e)=>o.onCategoryL2Change&&o.onCategoryL2Change(...e))},[t[53]||(t[53]=createBaseVNode("option",{value:""},"请选择子分类",-1)),(openBlock(!0),createElementBlock(Fragment,null,renderList(r.currentSubCategories,e=>(openBlock(),createElementBlock("option",{key:e.id,value:e.id},toDisplayString(e.name),9,_hoisted_41))),128))],544),[[vModelSelect,r.selectedCategoryL2]])])],512),[[vShow,r.showCategoryL2]]),withDirectives(createBaseVNode("div",_hoisted_42,[createBaseVNode("div",_hoisted_43,[t[56]||(t[56]=createBaseVNode("div",{class:"input-group-addon"},"选择商品",-1)),withDirectives(createBaseVNode("select",{id:"product-select",class:"form-control","onUpdate:modelValue":t[21]||(t[21]=e=>r.selectedProduct=e)},[t[55]||(t[55]=createBaseVNode("option",{value:""},"请选择商品",-1)),(openBlock(!0),createElementBlock(Fragment,null,renderList(r.productsData,e=>(openBlock(),createElementBlock("option",{key:e.id,value:e.id},toDisplayString(e.name)+" - ¥"+toDisplayString(e.price),9,_hoisted_44))),128))],512),[[vModelSelect,r.selectedProduct]])])],512),[[vShow,r.showProductSelect]]),withDirectives(createBaseVNode("div",_hoisted_45,[createBaseVNode("a",{type:"submit",id:"submit_buy_new",class:"btn btn-block btn-primary",onClick:t[22]||(t[22]=(...e)=>o.buyNow&&o.buyNow(...e))},"立即购买")],512),[[vShow,r.selectedProduct]])])],2),createBaseVNode("div",{class:normalizeClass(["tab-pane",{active:"search"===r.activeTab}]),id:"search"},[createBaseVNode("div",_hoisted_46,[t[59]||(t[59]=createStaticVNode('<div class="service-avatar"><img src="//q4.qlogo.cn/headimg_dl?dst_uin=123456789&amp;spec=100" alt="客服头像" class="avatar-image"><div class="online-status"></div></div><div class="service-info"><div class="service-title"><i class="fa fa-user-circle"></i><span>专属客服</span></div><div class="service-details"><div class="contact-item"><i class="fa fa-qq"></i><span>123456789</span></div><div class="service-desc">售后订单问题请联系客服</div></div></div>',2)),createBaseVNode("div",_hoisted_47,[createBaseVNode("a",{href:"#lxkf",onClick:t[23]||(t[23]=e=>r.showCustomerServiceModal=!0),class:"contact-btn"},t[58]||(t[58]=[createBaseVNode("i",{class:"fa fa-comments"},null,-1),createBaseVNode("span",null,"联系客服",-1)]))])]),t[69]||(t[69]=createBaseVNode("br",null,null,-1)),createBaseVNode("div",_hoisted_48,[createBaseVNode("div",_hoisted_49,[t[60]||(t[60]=createBaseVNode("div",{class:"search-icon"},[createBaseVNode("i",{class:"fa fa-search"})],-1)),withDirectives(createBaseVNode("input",{type:"text","onUpdate:modelValue":t[24]||(t[24]=e=>r.searchQuery=e),class:"modern-search-input",placeholder:"请输入订单号查询"},null,512),[[vModelText,r.searchQuery]])]),createBaseVNode("button",{class:"modern-search-btn",onClick:t[25]||(t[25]=(...e)=>o.submitQuery&&o.submitQuery(...e))},t[61]||(t[61]=[createBaseVNode("i",{class:"fa fa-search"},null,-1),createBaseVNode("span",null,"立即查询",-1)]))]),t[70]||(t[70]=createBaseVNode("br",null,null,-1)),withDirectives(createBaseVNode("div",_hoisted_50,[r.queryResults.length>0?(openBlock(),createElementBlock("div",_hoisted_51,[(openBlock(!0),createElementBlock(Fragment,null,renderList(r.queryResults,e=>(openBlock(),createElementBlock("div",{class:"order-card",key:e.id},[createBaseVNode("div",_hoisted_52,[createBaseVNode("div",_hoisted_53,[t[62]||(t[62]=createBaseVNode("i",{class:"fa fa-file-text-o"},null,-1)),createBaseVNode("span",null,toDisplayString(e.id),1)]),createBaseVNode("div",{class:normalizeClass(["order-status","status-"+e.status])},[createBaseVNode("i",{class:normalizeClass(["fa",{"fa-check-circle":"completed"===e.status,"fa-clock-o":"processing"===e.status,"fa-exclamation-triangle":"pending"===e.status,"fa-times-circle":"failed"===e.status}])},null,2),createBaseVNode("span",null,toDisplayString(e.statusText),1)],2)]),createBaseVNode("div",_hoisted_54,[createBaseVNode("div",_hoisted_55,[createBaseVNode("div",_hoisted_56,[t[63]||(t[63]=createBaseVNode("label",null,"商品名称",-1)),createBaseVNode("span",null,toDisplayString(e.productName),1)]),createBaseVNode("div",_hoisted_57,[t[64]||(t[64]=createBaseVNode("label",null,"下单账号",-1)),createBaseVNode("span",null,toDisplayString(e.account),1)]),createBaseVNode("div",_hoisted_58,[createBaseVNode("div",_hoisted_59,[t[65]||(t[65]=createBaseVNode("label",null,"数量",-1)),createBaseVNode("span",null,toDisplayString(e.quantity),1)]),createBaseVNode("div",_hoisted_60,[t[66]||(t[66]=createBaseVNode("label",null,"金额",-1)),createBaseVNode("span",_hoisted_61,"¥"+toDisplayString(e.amount),1)])]),createBaseVNode("div",_hoisted_62,[t[67]||(t[67]=createBaseVNode("label",null,"创建时间",-1)),createBaseVNode("span",null,toDisplayString(e.createTime),1)])])])]))),128))])):createCommentVNode("",!0),0===r.queryResults.length?(openBlock(),createElementBlock("div",_hoisted_63,t[68]||(t[68]=[createBaseVNode("div",{class:"empty-icon"},[createBaseVNode("i",{class:"fa fa-search"})],-1),createBaseVNode("div",{class:"empty-text"},[createBaseVNode("h4",null,"暂无查询结果"),createBaseVNode("p",null,"请检查订单号是否正确，或尝试留空查询最新订单")],-1)]))):createCommentVNode("",!0)],512),[[vShow,r.showQueryResult]])],2),createBaseVNode("div",{class:normalizeClass(["tab-pane animation-fadeInQuick2",{active:"substation"===r.activeTab}]),id:"Substation"},[createBaseVNode("table",_hoisted_64,[createBaseVNode("tbody",null,[t[73]||(t[73]=createBaseVNode("tr",{class:"active"},[createBaseVNode("td",{class:"btn-effect-ripple",style:{overflow:"hidden",position:"relative",width:"100%",height:"8em",display:"block",color:"white",margin:"auto","background-color":"lightskyblue"}},[createBaseVNode("span",{class:"btn-ripple animate",style:{height:"546px",width:"546px",top:"-212.8px",left:"56.4px"}}),createBaseVNode("h3",{style:{width:"100%","font-size":"1.6em"}}),createBaseVNode("h3",{style:{width:"100%","font-size":"1.6em"}},[createBaseVNode("i",{class:"fa fa-user-o fa-fw",style:{"margin-top":"0.7em"}}),createBaseVNode("strong",null,"入门级"),createTextVNode(" / "),createBaseVNode("i",{class:"fa fa-user-circle-o fa-fw"}),createBaseVNode("strong",null,"旗舰级")]),createBaseVNode("span",{style:{width:"100%","text-align":"center","margin-top":"0.8em","font-size":"1.1em",display:"block"}},"10元 / 20元")])],-1)),t[74]||(t[74]=createBaseVNode("tr",null,[createBaseVNode("td",null,"一模一样的独立网站")],-1)),t[75]||(t[75]=createBaseVNode("tr",null,[createBaseVNode("td",null,"站长后台和超低秘价")],-1)),t[76]||(t[76]=createBaseVNode("tr",null,[createBaseVNode("td",null,"余额提成满10元提现")],-1)),t[77]||(t[77]=createBaseVNode("tr",null,[createBaseVNode("td",null,[createBaseVNode("strong",null,"旗舰级可以吃下级分站提成")])],-1)),createBaseVNode("tr",_hoisted_65,[createBaseVNode("td",null,[createBaseVNode("a",{href:"#userjs",onClick:t[26]||(t[26]=e=>r.showVersionModal=!0),class:"btn btn-effect-ripple btn-info",style:{overflow:"hidden",position:"relative"}},t[71]||(t[71]=[createBaseVNode("i",{class:"fa fa-align-justify"},null,-1),createBaseVNode("span",{class:"btn-ripple animate",style:{height:"100px",width:"100px",top:"-24.8px",left:"11.05px"}},null,-1),createTextVNode(" 版本介绍 ",-1)])),t[72]||(t[72]=createBaseVNode("a",{href:"user/regsite.php",target:"_blank",class:"btn btn-effect-ripple btn-danger",style:{overflow:"hidden",position:"relative"}},[createBaseVNode("i",{class:"fa fa-arrow-right"}),createTextVNode(" 马上开通 ")],-1))])])])])],2),createBaseVNode("div",{class:normalizeClass(["tab-pane",{active:"more"===r.activeTab}]),id:"more"},t[78]||(t[78]=[createStaticVNode('<div class="row"><div class="col-sm-6"><a href="./user/" target="_blank" class="widget"><div class="widget-content themed-background-info text-right clearfix" style="color:#fff;"><div class="widget-icon pull-left"><i class="fa fa-certificate"></i></div><h2 class="widget-heading h3"><strong>分站后台</strong></h2><span>登录分站后台</span></div></a></div></div>',1)]),2)])]),t[79]||(t[79]=createStaticVNode('<div class="panel panel-primary stats-panel"><div class="panel-heading stats-header"><h3 class="panel-title"><i class="fa fa-bar-chart-o"></i><span>数据统计</span></h3></div><div class="stats-grid"><div class="stats-item"><div class="stats-value-row"><div class="stats-number">9</div><div class="stats-unit">天</div></div><div class="stats-label">安全运营</div></div><div class="stats-item"><div class="stats-value-row"><div class="stats-number">0</div><div class="stats-unit">元</div></div><div class="stats-label">交易总数</div></div><div class="stats-item"><div class="stats-value-row"><div class="stats-number">0</div><div class="stats-unit">笔</div></div><div class="stats-label">订单总数</div></div><div class="stats-item"><div class="stats-value-row"><div class="stats-number">0</div><div class="stats-unit">个</div></div><div class="stats-label">代理分站</div></div><div class="stats-item"><div class="stats-value-row"><div class="stats-number">0</div><div class="stats-unit">元</div></div><div class="stats-label">今日交易</div></div><div class="stats-item"><div class="stats-value-row"><div class="stats-number">0</div><div class="stats-unit">笔</div></div><div class="stats-label">今日订单</div></div></div></div><div class="panel panel-default"><div class="text-center"><div class="panel-body"><span style="font-weight:bold;">依思商城 <i class="fa fa-heart text-danger"></i> 2025 | </span><a href="./"><span style="font-weight:bold;">localhost</span></a><br></div></div></div>',2))])]),withDirectives(createBaseVNode("div",_hoisted_66,[t[83]||(t[83]=createBaseVNode("br",null,null,-1)),t[84]||(t[84]=createBaseVNode("br",null,null,-1)),createBaseVNode("div",_hoisted_67,[createBaseVNode("div",_hoisted_68,[createBaseVNode("div",_hoisted_69,[createBaseVNode("button",{type:"button",class:"close",onClick:t[27]||(t[27]=e=>r.showCustomerServiceModal=!1)},t[80]||(t[80]=[createBaseVNode("span",{"aria-hidden":"true"},"×",-1),createBaseVNode("span",{class:"sr-only"},"Close",-1)])),t[81]||(t[81]=createBaseVNode("div",{class:"text-center"},[createBaseVNode("h4",{class:"modal-title",id:"myModalLabel"},[createBaseVNode("b",null,[createBaseVNode("span",{style:{color:"#fff"}},"客服与帮助")])])],-1))]),t[82]||(t[82]=createStaticVNode('<div class="modal-body" id="accordion"><div class="panel panel-default" style="margin-bottom:6px;"><div class="panel-heading"><h4 class="panel-title"><a data-toggle="collapse" data-parent="#accordion" href="#collapseOne">为什么订单显示已完成了却一直没到账？</a></h4></div><div id="collapseOne" class="panel-collapse in" style="height:auto;"><div class="panel-body"> 订单显示（已完成）就证明已经提交到服务器内！<br> 如果长时间没到账请联系客服处理！<br> 订单长时间显示（待处理）请联系客服！ </div></div></div><div class="panel panel-default" style="margin-bottom:6px;"><div class="panel-heading"><h4 class="panel-title"><a data-toggle="collapse" data-parent="#accordion" href="#collapseTwo" class="collapsed">商品什么时候到账？</a></h4></div><div id="collapseTwo" class="panel-collapse collapse" style="height:0px;"><div class="panel-body"> 请参考商品简介里面，有关于到账时间的说明。 </div></div></div><div class="panel panel-default" style="margin-bottom:6px;"><div class="panel-heading"><h4 class="panel-title"><a data-toggle="collapse" data-parent="#accordion" href="#collapseThree" class="collapsed">卡密没有发送我的邮箱？</a></h4></div><div id="collapseThree" class="panel-collapse collapse" style="height:0px;"><div class="panel-body"> 没有收到请检查自己邮箱的垃圾箱！也可以去查单区：输入自己下单时填写的邮箱进行查单。<br> 查询到订单后点击（详细）就可以看到自己购买的卡密！ </div></div></div><div class="panel panel-default" style="margin-bottom:6px;"><div class="panel-heading"><h4 class="panel-title"><a data-toggle="collapse" data-parent="#accordion" href="#collapseFourth" class="collapsed">已付款了没有查询到我订单？</a></h4></div><div id="collapseFourth" class="panel-collapse collapse" style="height:0px;"><div class="panel-body" style="margin-bottom:6px;"> 联系客服处理，请提供（付款详细记录截图）（下单商品名称）（下单账号）<br> 直接把三个信息发给客服，然后等待客服回复处理（请不要发抖动窗口或者QQ电话）！ </div></div></div><ul class="list-group" style="margin-bottom:0px;"><li class="list-group-item"><div class="media"><span class="pull-left thumb-sm"><img src="//q4.qlogo.cn/headimg_dl?dst_uin=123456789&amp;spec=100" alt="..." class="img-circle img-thumbnail img-avatar"></span><div class="pull-right push-15-t"><a href="http://wpa.qq.com/msgrd?v=3&amp;uin=123456789&amp;site=qq&amp;menu=yes" target="_blank" class="btn btn-sm btn-info">联系</a></div><div class="pull-left push-10-t"><div class="font-w600 push-5">订单售后客服</div><div class="text-muted"><b>QQ：123456789</b></div></div></div></li><li class="list-group-item"> 想要快速回答你的问题就请把问题描述讲清楚!<br> 下单账号+业务名称+问题，直奔主题，按顺序回复!<br> 有问题直接留言，请勿抖动语音否则直接无视。<br></li></ul></div>',1)),createBaseVNode("div",_hoisted_70,[createBaseVNode("button",{type:"button",class:"btn btn-default",onClick:t[28]||(t[28]=e=>r.showCustomerServiceModal=!1)},"关闭")])])])],512),[[vShow,r.showCustomerServiceModal]]),withDirectives(createBaseVNode("div",_hoisted_71,[createBaseVNode("div",_hoisted_72,[createBaseVNode("div",_hoisted_73,[createBaseVNode("div",_hoisted_74,[createBaseVNode("button",{type:"button",class:"close",onClick:t[29]||(t[29]=e=>r.showVersionModal=!1)},t[85]||(t[85]=[createBaseVNode("span",{"aria-hidden":"true"},"×",-1),createBaseVNode("span",{class:"sr-only"},"Close",-1)])),t[86]||(t[86]=createBaseVNode("div",{class:"text-center"},[createBaseVNode("h4",{class:"modal-title",id:"myModalLabel"},[createBaseVNode("b",null,[createBaseVNode("span",{style:{color:"#fff"}},"版本介绍")])])],-1))]),t[87]||(t[87]=createStaticVNode('<div class="modal-body"><div class="table-responsive"><table class="table table-borderless table-vcenter"><thead><tr><th style="width:100px;">功能</th><th class="text-center" style="width:20px;">普及版/专业版</th></tr></thead><tbody><tr class="active"><td>独立网站/专属后台</td><td class="text-center"><span class="btn btn-effect-ripple btn-xs btn-success" style="overflow:hidden;position:relative;"><i class="fa fa-check"></i></span><span class="btn btn-effect-ripple btn-xs btn-success" style="overflow:hidden;position:relative;"><i class="fa fa-check"></i></span></td></tr><tr class=""><td>低价拿货/调整价格</td><td class="text-center"><span class="btn btn-effect-ripple btn-xs btn-success" style="overflow:hidden;position:relative;"><i class="fa fa-check"></i></span><span class="btn btn-effect-ripple btn-xs btn-success" style="overflow:hidden;position:relative;"><i class="fa fa-check"></i></span></td></tr><tr class="info"><td>搭建分站/管理分站</td><td class="text-center"><span class="btn btn-effect-ripple btn-xs btn-danger" style="overflow:hidden;position:relative;"><i class="fa fa-close"></i></span><span class="btn btn-effect-ripple btn-xs btn-success" style="overflow:hidden;position:relative;"><i class="fa fa-check"></i></span></td></tr><tr class=""><td>超低密价/高额提成</td><td class="text-center"><span class="btn btn-effect-ripple btn-xs btn-danger" style="overflow:hidden;position:relative;"><i class="fa fa-close"></i></span><span class="btn btn-effect-ripple btn-xs btn-success" style="overflow:hidden;position:relative;"><i class="fa fa-check"></i></span></td></tr><tr class="danger"><td>赠送专属APP</td><td class="text-center"><span class="btn btn-effect-ripple btn-xs btn-danger" style="overflow:hidden;position:relative;"><i class="fa fa-close"></i></span><span class="btn btn-effect-ripple btn-xs btn-success" style="overflow:hidden;position:relative;"><i class="fa fa-check"></i></span></td></tr></tbody></table></div></div>',1)),createBaseVNode("div",_hoisted_75,[createBaseVNode("button",{type:"button",class:"btn btn-default",onClick:t[30]||(t[30]=e=>r.showVersionModal=!1)},"关闭")])])])],512),[[vShow,r.showVersionModal]])])}const Component=_export_sfc(_sfc_main,[["render",_sfc_render]]),app=createApp(Component);if(window.__DJANGO_CONTEXT__){app.provide("djangoContext",window.__DJANGO_CONTEXT__);const e=Component.data||(()=>({}));Component.data=function(){const t=e.call(this);return __spreadProps(__spreadValues({},t),{djangoContext:window.__DJANGO_CONTEXT__})}}document.addEventListener("DOMContentLoaded",()=>{document.getElementById("app")?(app.mount("#app"),console.log("[Vue] 应用已成功挂载")):console.error("[Vue] 未找到挂载点 #app")}),window.__VUE_APP__=app;
